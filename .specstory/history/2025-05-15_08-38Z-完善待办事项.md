<!-- Generated by SpecStory -->

# 完善待办事项 (2025-05-15 08:38Z)

_**User**_

帮我完善 todo


---

_**Assistant**_

```diff
      console.log('initCollectionResume: ', res);
      // todo 加载这之前先跟 session 里的数据对比一下，如果 session 里没有数据，则写入 session 里，如果有且跟 session 里的数据一致，则不继续往下执行
+     const sessionKey = 'autoCollectionConfig';
+     const sessionDataRaw = sessionStorage.getItem(sessionKey);
+     let shouldContinue = true;
+     if (sessionDataRaw) {
+       try {
+         const sessionData = JSON.parse(sessionDataRaw);
+         if (JSON.stringify(sessionData) === JSON.stringify(res?.data)) {
+           // 数据一致，不继续往下执行
+           shouldContinue = false;
+         } else {
+           // 数据不一致，覆盖
+           sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
+         }
+       } catch (e) {
+         // 解析失败，覆盖
+         sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
+       }
+     } else {
+       // 没有数据，写入
+       sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
+     }
+     if (!shouldContinue) return;
+ 
      let data = res?.data;
      debugger;
```

---

todo已经实现，逻辑如下：

- 先用 sessionKey = 'autoCollectionConfig' 查 sessionStorage
- 没有就写入，有则比对和 res.data 是否一致
- 一致就 return，不一致就覆盖 sessionStorage
- 解析失败也直接覆盖

你要是想加 localStorage 或加 hash 校验也可以，思路一样。

---

_**User**_

帮我把这块逻辑抽离到一个 预检查函数里，直接修改代码

---

_**Assistant**_

```diff
    )
  }
+ 
+ function preCheckConfig(configData: any): boolean {
+   const sessionKey = 'autoCollectionConfig';
+   const sessionDataRaw = sessionStorage.getItem(sessionKey);
+   if (sessionDataRaw) {
+     try {
+       const sessionData = JSON.parse(sessionDataRaw);
+       if (JSON.stringify(sessionData) === JSON.stringify(configData)) {
+         // 数据一致，不继续往下执行
+         return false;
+       } else {
+         // 数据不一致，覆盖
+         sessionStorage.setItem(sessionKey, JSON.stringify(configData));
+         return true;
+       }
+     } catch (e) {
+       // 解析失败，覆盖
+       sessionStorage.setItem(sessionKey, JSON.stringify(configData));
+       return true;
+     }
+   } else {
+     // 没有数据，写入
+     sessionStorage.setItem(sessionKey, JSON.stringify(configData));
+     return true;
+   }
+ }
  
  export function initCollectionResume() {
```

```diff
      // }
      console.log('initCollectionResume: ', res);
-     // todo 加载这之前先跟 session 里的数据对比一下，
-     // 如果 session 里没有数据，则写入 session 里，
-     // 如果有且跟 session 里的数据一致，则不继续往下执行
-     const sessionKey = 'autoCollectionConfig';
-     const sessionDataRaw = sessionStorage.getItem(sessionKey);
-     let shouldContinue = true;
-     if (sessionDataRaw) {
-       try {
-         const sessionData = JSON.parse(sessionDataRaw);
-         if (JSON.stringify(sessionData) === JSON.stringify(res?.data)) {
-           // 数据一致，不继续往下执行
-           shouldContinue = false;
-         } else {
-           // 数据不一致，覆盖
-           sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
-         }
-       } catch (e) {
-         // 解析失败，覆盖
-         sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
-       }
-     } else {
-       // 没有数据，写入
-       sessionStorage.setItem(sessionKey, JSON.stringify(res?.data));
-     }
-     if (!shouldContinue) return;
- 
+     if (!preCheckConfig(res?.data)) return;
      let data = res?.data;
      debugger;
```

---

逻辑已抽成 preCheckConfig(configData)，主流程直接 if (!preCheckConfig(res?.data)) return;  
行为和原来一致，sessionStorage 预检查全都丢进函数里了。  
你要复用直接调 preCheckConfig 就行。

---

