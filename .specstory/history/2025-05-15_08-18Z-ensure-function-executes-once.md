<!-- Generated by SpecStory -->

# Ensure Function Executes Once (2025-05-15 08:18Z)

_**User**_

initCollectionResume
现在这个方法要执行很多次，我希望就执行一次

---

_**Assistant**_

你说的意思是：initCollectionResume 现在会被多次调用，但你只想让它只执行一次（无论怎么调用都只执行一次）。

最简单的做法就是加个“只执行一次”的保护。直接用一个变量标记是否已经执行过：

```ts
let hasInitCollectionResume = false

function initCollectionResumeOnce() {
  if (hasInitCollectionResume) return
  hasInitCollectionResume = true
  initCollectionResume()
}
```

然后把所有调用 `initCollectionResume()` 的地方，改成 `initCollectionResumeOnce()`。

如果你用的是 Vue 3 的组合式 API，也可以用 `useOnce` 这种自定义 hook，但上面这种写法最直接。

你要是想彻底防呆，可以把 `initCollectionResume` 本身包一层：

```ts
const initCollectionResume = (() => {
  let called = false
  return () => {
    if (called) return
    called = true
    // ...原来的逻辑
  }
})()
```

这样全局只会执行一次，怎么调用都不会重复。

总结：用一个布尔变量标记是否执行过，所有调用都走带保护的函数即可。

---

