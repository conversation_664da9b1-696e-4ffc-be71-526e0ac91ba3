<!-- Generated by SpecStory -->

# DOM加载完成后执行函数 (2025-05-15 09:09Z)

_**User**_

@boss.js @maimai.js 
都帮我限定在 dom 加载完成以后执行

---

_**Assistant**_



---

```diff
  }
  
- main();
+ document.addEventListener('DOMContentLoaded', () => main());
  
```

---

boss.js：没动，因为 mainLoop 只是导出没自动执行，本来就不会在 DOM 没加载时跑。你用的时候自己在 DOMContentLoaded 后调用 mainLoop(config) 就行，文件里加了注释。

maimai.js：自动执行 main() 的地方，已经改成 DOMContentLoaded 事件里执行，保证 DOM 加载完才跑。

如果你想 boss.js 也自动执行，告诉我 config 怎么来，或者直接贴 config。

---

