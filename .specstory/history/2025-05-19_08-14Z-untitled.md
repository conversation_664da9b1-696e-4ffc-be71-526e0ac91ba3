<!-- Generated by SpecStory -->

# Untitled (2025-05-19 08:14Z)

_**User**_

@main.vue 
@liepin.ts 
我希望 执行 完成 handleLiepinClosePage 里的方法后再执行，collectResume(document.querySelector('#resume-detail-single'))，请帮我优化代码执行，保证代码不报错

---

_**Assistant**_

你要保证 `handleLiepinClosePage` 里的逻辑（包括异步和 window.close）执行完毕后，再执行 `collectResume(document.querySelector('#resume-detail-single'))`，而不是并发执行。

目前 main.vue 里是这样：

```js
else if (isLiepin) setTimeout(async () => {
  // 处理猎聘详情关闭页面
  // add in 2025-05-19
  await handleLiepinClosePage()

  collectResume(document.querySelector('#resume-detail-single'))
}, 1000)
```

但 `handleLiepinClosePage` 里其实是 setTimeout + await sleepRandom + window.close()，window.close() 后页面就没了，后面的代码不会执行。

**你想要的效果应该是：只有在不关闭页面的情况下才 collectResume，否则就不 collectResume。**

### 解决方案

1. **让 handleLiepinClosePage 返回是否已关闭页面。**
2. **main.vue 里根据返回值决定是否 collectResume。**

---

#### 1. 修改 handleLiepinClosePage

让它返回 true/false，表示是否已关闭页面：

```ts
// src/content/resume/liepin.ts
export async function handleLiepinClosePage(): Promise<boolean> {
  const randomDelay = Math.floor(Math.random() * (10000 - 5000 + 1)) + 5000;

  return new Promise((resolve) => {
    setTimeout(async () => {
      if (!window.location.href.includes('/resume/showresumedetail/')) {
        resolve(false);
        return;
      }

      if (document.referrer.includes('https://h.liepin.com/im/showmsgnewpage')) {
        document.querySelector('.resume-operation-btn')?.dispatchEvent(new MouseEvent('click'));
        await sleepRandom(5000, 8000);
        window.opener.postMessage('resume_done', '*');
        await sleepRandom(2000, 3000);
        window.close();
        resolve(true); // 页面即将关闭
      } else {
        resolve(false);
      }
    }, randomDelay);
  });
}
```
- 用 Promise 包裹，resolve(true) 表示页面已关闭，false 表示未关闭。
- 用 dispatchEvent 规避 TS 报错（Element 可能不是 HTMLElement）。

---

#### 2. main.vue 里这样用

```ts
else if (isLiepin) setTimeout(async () => {
  const closed = await handleLiepinClosePage();
  if (!closed) {
    collectResume(document.querySelector('#resume-detail-single'));
  }
}, 1000)
```

---

**这样保证：只有在页面未被 handleLiepinClosePage 关闭时，才会 collectResume。不会报错，也不会 collectResume 在页面即将关闭时执行。**

---

如果你还有别的需求，比如“即使页面关闭也要 collectResume”，那就只能把 collectResume 放到 handleLiepinClosePage 里合适的位置，但一般没意义，因为 window.close() 后代码不会再执行。

---

**总结：**
- handleLiepinClosePage 返回 Promise<boolean>
- main.vue 里根据返回值决定是否 collectResume

如需完整代码diff，告诉我。

---

