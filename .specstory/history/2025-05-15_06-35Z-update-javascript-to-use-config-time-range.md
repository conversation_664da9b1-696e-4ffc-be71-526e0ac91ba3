<!-- Generated by SpecStory -->

# Update JavaScript to Use Config Time Range (2025-05-15 06:35Z)

_**User**_

config.timeRange = [ "00:00", "06:00" ]
=============
@boss.js  @maimai.js  都改为从config.timeRange 里读取开始时间，如果没有时间的话不执行

---

_**Assistant**_

<details>
            <summary>Searched codebase "parse timeRange from config and check current time" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/content/resume/auto-collection/index.ts` | L13-51 |
| `src/content/resume/auto-collection/liepin.js` | L1-63 |
| `src/content/resume/auto-collection/index.ts` | L1-13 |
| `src/content/resume/auto-collection/utils/index.js` | L240-293 |
| `src/content/resume/auto-collection/utils/index.js` | L190-240 |
| `src/content/resume/auto-collection/boss.js` | L1-25 |
| `src/content/resume/auto-collection/liepin.js` | L126-163 |
| `src/content/resume/auto-collection/boss.js` | L57-83 |
| `src/content/resume/auto-collection/liepin.js` | L468-514 |
| `src/content/resume/auto-collection/liepin.js` | L300-332 |
| `src/content/resume/auto-collection/liepin.js` | L63-126 |
| `src/content/resume/auto-collection/utils/index.js` | L92-104 |
| `src/content/resume/auto-collection/maimai.js` | L43-50 |
| `src/content/resume/auto-collection/boss.js` | L25-57 |
| `src/content/resume/auto-collection/utils/index.js` | L109-132 |
| `src/content/resume/auto-collection/maimai.js` | L1-43 |
| `src/content/resume/auto-collection/liepin.js` | L433-468 |
| `src/content/resume/auto-collection/liepin.js` | L394-433 |
| `src/content/resume/auto-collection/liepin.js` | L243-300 |
| `src/content/resume/auto-collection/utils/index.js` | L104-109 |
| `src/content/resume/auto-collection/utils/index.js` | L4-19 |
| `src/content/resume/auto-collection/liepin.js` | L332-394 |
| `src/content/resume/auto-collection/utils/index.js` | L1-4 |
| `src/content/resume/auto-collection/utils/index.js` | L157-190 |
| `src/content/resume/auto-collection/utils/index.js` | L23-34 |

</details>

---

```diff
  
  export async function mainLoop(config) {
+   // 时间段判断
+   if (Array.isArray(config?.timeRange) && config.timeRange.length === 2) {
+     const now = new Date();
+     const pad = n => n.toString().padStart(2, '0');
+     const cur = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
+     const [start, end] = config.timeRange;
+     if (!(start <= cur && cur <= end)) {
+       console.log('当前不在设定时间段内，跳过执行', cur, config.timeRange);
+       return;
+     }
+   } else {
+     console.log('未配置timeRange，跳过执行');
+     return;
+   }
    console.log('boss 开始: ', config);
    debugger;
```

---

```diff
  
  async function main(config) {
+   // 时间段判断
+   if (Array.isArray(config?.timeRange) && config.timeRange.length === 2) {
+     const now = new Date();
+     const pad = n => n.toString().padStart(2, '0');
+     const cur = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
+     const [start, end] = config.timeRange;
+     if (!(start <= cur && cur <= end)) {
+       console.log('当前不在设定时间段内，跳过执行', cur, config.timeRange);
+       return;
+     }
+   } else {
+     console.log('未配置timeRange，跳过执行');
+     return;
+   }
    await sleepRandom(3000, 5000);
    const msgList = await waitForElm('.message-list');
```

---

你要的效果已经加好了：

- boss.js 和 maimai.js 的主函数开头都加了时间段判断，只有当前时间在 config.timeRange 里才会继续执行，否则直接 return。
- 没配 timeRange 也直接 return。

用法就是 config.timeRange = [ "00:00", "06:00" ] 这种，只有 0~6 点会跑，其他时间直接跳过。

如果你想支持跨天（比如 ["23:00", "06:00"]），现在的实现不支持，需要再改。要的话说一声。

其它你没想到的点：
- 现在是字符串比较，"06:00" > "23:00" 这种会有问题（跨天），常规时间段没问题。
- 你要支持多段时间，或者更复杂的规则，也可以加。

有别的需求直接说。

---

