<!-- Generated by SpecStory -->

# 创建 mobileconfig 文件以添加 crx (2025-07-02 09:38Z)

_**User**_

帮我写个 「mobileconfig」把crx 添加到注册表

---

_**Assistant**_

你要的是一个 .mobileconfig 文件（Apple 配置描述文件），用于在 macOS 上把 Chrome 扩展（.crx）注册到系统的受管扩展（Managed Extensions）里。这样可以让 Chrome 自动安装/信任你的 .crx 扩展。

核心点：
- 你需要有扩展的 ID（可以用 crx 文件解包或者用 chrome://extensions 查看）。
- 你需要有扩展的下载 URL（比如你本地服务器或公网可访问的 .crx 链接）。

下面是一个典型的 mobileconfig 示例，把你的 crx 加到 Chrome 的注册表（其实是 com.google.Chrome 的 managed preferences）：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>PayloadContent</key>
  <array>
    <dict>
      <key>PayloadType</key>
      <string>com.apple.ManagedClient.preferences</string>
      <key>PayloadVersion</key>
      <integer>1</integer>
      <key>PayloadIdentifier</key>
      <string>com.yourcompany.chromeextension</string>
      <key>PayloadUUID</key>
      <string>REPLACE-WITH-UUID</string>
      <key>PayloadEnabled</key>
      <true/>
      <key>PayloadDisplayName</key>
      <string>Chrome Extension Install</string>
      <key>PayloadOrganization</key>
      <string>Your Company</string>
      <key>PayloadDescription</key>
      <string>Force install Chrome extension</string>
      <key>PayloadContent</key>
      <dict>
        <key>com.google.Chrome</key>
        <dict>
          <key>Forced</key>
          <array>
            <dict>
              <key>mcx_preference_settings</key>
              <dict>
                <key>ExtensionInstallForcelist</key>
                <array>
                  <string>扩展ID;https://你的crx下载地址/your.crx</string>
                </array>
              </dict>
            </dict>
          </array>
        </dict>
      </dict>
    </dict>
  </array>
  <key>PayloadType</key>
  <string>Configuration</string>
  <key>PayloadVersion</key>
  <integer>1</integer>
  <key>PayloadIdentifier</key>
  <string>com.yourcompany.chromeextension.profile</string>
  <key>PayloadUUID</key>
  <string>REPLACE-WITH-ANOTHER-UUID</string>
  <key>PayloadDisplayName</key>
  <string>Chrome Extension Profile</string>
  <key>PayloadDescription</key>
  <string>Profile to force install Chrome extension</string>
  <key>PayloadOrganization</key>
  <string>Your Company</string>
</dict>
</plist>
```

**你要做的：**
1. 把 `扩展ID` 换成你的 crx 的扩展 ID（比如：abcdefghijklmnoabcdefhijklmnoabc）。
2. 把 `https://你的crx下载地址/your.crx` 换成你实际的 crx 文件 URL。
3. `PayloadUUID` 用 uuidgen 生成（终端输入 uuidgen 拿到一个 UUID）。
4. 保存为 `chrome-extension.mobileconfig`，双击安装即可。

**注意：**
- 这种方式适合 MDM 管理或者本地测试，普通用户装了没啥用，得配合 Chrome 的企业策略。
- 你本地的 crx 链接要能被 Chrome 访问到（本地服务器或公网）。

如果你要自动生成 PayloadUUID，可以在终端用 uuidgen 两次，分别替换上面两个 UUID 字段。

有问题可以直接贴你的 crx ID 和下载地址，我帮你生成完整 mobileconfig。

---

