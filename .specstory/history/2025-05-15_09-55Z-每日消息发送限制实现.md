<!-- Generated by SpecStory -->

# 每日消息发送限制实现 (2025-05-15 09:55Z)

_**User**_

@boss.js @maimai.js 
帮我限制每天最多发20条信息

---

_**Assistant**_

你要在 boss.js 和 maimai.js 里加一个“每日最多20条消息”的限制。核心点：

- 用 localStorage 记录每天已发消息数和日期
- 超过20条就 return，不再发
- 新一天自动重置计数

下面是具体怎么改：

---

### boss.js

在 mainLoop 里加：

```js
// ... existing code ...
  // === 新增：每日消息数限制 ===
  const today = new Date().toISOString().slice(0, 10);
  let sent = JSON.parse(localStorage.getItem('boss_sent') || '{}');
  if (sent.date !== today) sent = { date: today, count: 0 };
  if (sent.count >= 20) {
    console.log('今日已达上限，跳过');
    return;
  }
  // ... existing code ...
      // 在每次真正发消息后加：
      if (input && !randomChance(0.1)) {
        // ... existing code ...
        sent.count += 1;
        localStorage.setItem('boss_sent', JSON.stringify(sent));
        if (sent.count >= 20) {
          console.log('今日已达上限，跳出');
          return;
        }
      }
  // ... existing code ...
```

---

### maimai.js

在 main 里加：

```js
// ... existing code ...
  // === 新增：每日消息数限制 ===
  const today = new Date().toISOString().slice(0, 10);
  let sent = JSON.parse(localStorage.getItem('maimai_sent') || '{}');
  if (sent.date !== today) sent = { date: today, count: 0 };
  if (sent.count >= 20) {
    console.log('今日已达上限，跳过');
    return;
  }
  // ... existing code ...
    // 在每次真正发消息后加：
    if (input) {
      // ... existing code ...
      sent.count += 1;
      localStorage.setItem('maimai_sent', JSON.stringify(sent));
      if (sent.count >= 20) {
        console.log('今日已达上限，跳出');
        return;
      }
    }
  // ... existing code ...
```

---

这样每个脚本每天最多发20条，第二天自动重置。你可以把这段逻辑抽成 util 复用，但直接写死也没问题。

---

_**User**_

抽象在 util 里限制

---

_**Assistant**_

直接抽 util，建议叫 limitDailyCount，支持不同 key。用法：每次发消息前 check，发完后 inc。

src/content/resume/auto-collection/utils/limitDailyCount.js：

```js
// 限制每天最多 count 次，key 用于区分不同脚本
export function getDailyCount(key) {
  const today = new Date().toISOString().slice(0, 10);
  let data = JSON.parse(localStorage.getItem(key) || '{}');
  if (data.date !== today) data = { date: today, count: 0 };
  return data.count;
}

export function incDailyCount(key) {
  const today = new Date().toISOString().slice(0, 10);
  let data = JSON.parse(localStorage.getItem(key) || '{}');
  if (data.date !== today) data = { date: today, count: 0 };
  data.count += 1;
  localStorage.setItem(key, JSON.stringify(data));
}

export function isDailyLimitReached(key, limit) {
  return getDailyCount(key) >= limit;
}
```

---

boss.js/maimai.js 里用：

```js
import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'

// ... existing code ...
if (isDailyLimitReached('boss_sent', 20)) {
  console.log('今日已达上限，跳过');
  return;
}
// ...每次真正发消息后
incDailyCount('boss_sent');
if (isDailyLimitReached('boss_sent', 20)) {
  console.log('今日已达上限，跳出');
  return;
}
```

maimai.js 只把 key 换成 'maimai_sent'。

这样以后别的脚本也能用，key 随便起。

---

_**User**_

帮我直接写代码，谢谢

---

_**Assistant**_

```diff
- 
+ export function getDailyCount(key) {
+   const today = new Date().toISOString().slice(0, 10);
+   let data = JSON.parse(localStorage.getItem(key) || '{}');
+   if (data.date !== today) data = { date: today, count: 0 };
+   return data.count;
+ }
+ 
+ export function incDailyCount(key) {
+   const today = new Date().toISOString().slice(0, 10);
+   let data = JSON.parse(localStorage.getItem(key) || '{}');
+   if (data.date !== today) data = { date: today, count: 0 };
+   data.count += 1;
+   localStorage.setItem(key, JSON.stringify(data));
+ }
+ 
+ export function isDailyLimitReached(key, limit) {
+   return getDailyCount(key) >= limit;
+ } 
```

---

```diff
  import { sleepRandom, waitForElm, randomChance, moveMouseAround, typeLikeHuman, mouseMoveTo, simulateHumanClick, getRandomDelay, delay, waitForElement, generateMousePath, simulateHumanMouseMovement, initAntiDetection } from './utils/'
+ import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'
  
  export async function mainLoop(config) {
```

```diff
      return;
    }
+   // === 每日消息数限制 ===
+   if (isDailyLimitReached('boss_sent', 20)) {
+     console.log('今日已达上限，跳过');
+     return;
+   }
    console.log('boss 开始: ', config);
    // debugger;
```

```diff
      if (!items.length) break;
      for (let i = 0; i < items.length; i++) {
+       if (isDailyLimitReached('boss_sent', 20)) {
+         console.log('今日已达上限，跳出');
+         return;
+       }
        console.log('boss 开始: ', i);
        const item = items[i];
```

```diff
            await typeLikeHuman(input, config?.contactContent || replies[Math.floor(Math.random() * replies.length)], 80, 200);
            await sleepRandom(2500, 5000);
+           incDailyCount('boss_sent');
          }
        }
```

---

```diff
  import { sleepRandom, waitForElm, randomChance, moveMouseAround, typeLikeHuman, mouseMoveTo, simulateHumanClick, getRandomDelay, delay, waitForElement, generateMousePath, simulateHumanMouseMovement, initAntiDetection } from './utils/'
+ import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'
  
  async function main(config) {
```

```diff
      return;
    }
+   // === 每日消息数限制 ===
+   if (isDailyLimitReached('maimai_sent', 20)) {
+     console.log('今日已达上限，跳过');
+     return;
+   }
    await sleepRandom(3000, 5000);
    const msgList = await waitForElm('.message-list');
    if (!msgList) return;
    const items = document.querySelectorAll('.message-item');
    for (let i = 1; i < items.length; i++) {
+     if (isDailyLimitReached('maimai_sent', 20)) {
+       console.log('今日已达上限，跳出');
+       return;
+     }
      console.log('开始: ', i);
      items[i].click();
```

```diff
        await typeLikeHuman(input, replies[Math.floor(Math.random() * replies.length)], 80, 200);
        await sleepRandom(2500, 5000);
+       incDailyCount('maimai_sent');
      }
  
```

---

都写好了：

- 新建了 `utils/limitDailyCount.js`，导出 getDailyCount、incDailyCount、isDailyLimitReached
- boss.js 和 maimai.js 都加了每日20条限制，发消息后自动 inc，key 分别用 boss_sent/maimai_sent
- 逻辑是：每天自动重置，超限直接 return

你直接用就行，别的脚本要用也一样写法。

---

