<!-- Generated by SpecStory -->

# 列出所有项目的 request.post 和 request.get (2025-07-04 05:27Z)

_**User**_

帮我 @/src 目录下 把所有项目的 「request.post」「request.get」都整体找出来列一下，中文回复

---

_**Assistant**_

<details>
            <summary>Searched codebase "Where are request.post and request.get used?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/utils/plugin-request.ts` | L45-49 |
| `src/utils/plugin-request.ts` | L18-41 |
| `src/utils/plugin-request.ts` | L49-54 |
| `src/utils/plugin-request.ts` | L41-45 |
| `src/utils/plugin-request.ts` | L2-6 |
| `src/background/index.ts` | L41-53 |
| `src/background/index.ts` | L30-41 |
| `src/content/resume/ai/coze.js` | L2-8 |
| `src/utils/plugin-request.ts` | L6-18 |
| `src/utils/track.ts` | L68-128 |
| `src/background/index.ts` | L22-30 |
| `src/content/resume/ai/coze.js` | L20-38 |
| `src/content/resume/ai/coze.js` | L8-20 |
| `src/background/index.ts` | L62-67 |
| `src/background/index.ts` | L72-100 |
| `src/background/index.ts` | L100-120 |
| `src/content/communication.ts` | L51-119 |
| `src/content/resume/auto-collection/index.ts` | L1-16 |
| `src/content/inject.ts` | L1-41 |
| `src/background/index.ts` | L11-22 |
| `src/background/index.ts` | L57-62 |
| `src/content/message/handlers/liepin-handler.ts` | L299-337 |
| `src/content/message/message.vue` | L1-58 |
| `src/content/message/handlers/linkedin-handler.ts` | L204-222 |
| `src/content/message/handlers/liepin-handler.ts` | L1-19 |

</details>

---

<details>
            <summary>Grep search for "request\.post\(|request\.get\(" • **13** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/components/job-search.vue` | L60 | `const res = await request.post(`${config.api}/api/company/${userStore.companyId}/pipeline/job/require/search`, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/utils/track.ts` | L85 | `request.post(API_URL, { data: [event] })` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/components/duplicate-talents.vue` | L64 | `return request.get(`${config.api}/api/talent/detail?talentId=13241486`)` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/components/talent-follow-up.vue` | L32 | `const res = await request.get(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/components/talent-job.vue` | L50 | `const res = await request.get(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/ai/coze.js` | L6 | `return request.get(config.api + `/api/company/1/ai/coze/config`)` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/ai/coze.js` | L11 | `return request.post(config.api + `/api/company/1/ai/chatParams/get`, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/ai/coze.js` | L34 | `return request.post(config.api + `/api/company/1/ai/coze/appId/${cfg?.appId}/workflowId/${cfg?.onlineChatFlowId}`, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/linkedin-handler.ts` | L213 | `const response = await request.get(apiUrl);` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/linkedin-handler.ts` | L325 | `const response = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/linkedin-handler.ts` | L358 | `const cozeResponse = await request.post(cozeUrl, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/auto-collection/index.ts` | L12 | `return await request.get(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/zhipin-handler.ts` | L157 | `//   const response = await request.get(apiUrl);` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/zhipin-handler.ts` | L199 | `const response = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/zhipin-handler.ts` | L232 | `const cozeResponse = await request.post(cozeUrl, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/liepin-handler.ts` | L181 | `const response = await request.get(apiUrl);` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/liepin-handler.ts` | L317 | `const response = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/handlers/liepin-handler.ts` | L351 | `const cozeResponse = await request.post(cozeUrl, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L257 | `const response = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L330 | `const jobres = await request.get(config.api + `/api/company/${userStore.companyId}/pipeline/job/require/pipeline/by/talent/${talentId}`)` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L331 | `const followupres = await request.get(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L344 | `const res = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L354 | `const res = await request.post(`${config.api}/api/talent/createOrUpdate`, { data: talent })` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L362 | `const res = await request.post(`${config.api}/api/company/${userStore.companyId}/pipeline/addToJobRequirement`, {` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L434 | `const res = await request.get(`${config.api}/api/talent/checkDuplicate`, { params })` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L441 | `const mergedTalentData = await request.post(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/resume/main.vue` | L485 | `const res = await request.get(`${config.api}/api/company/checkLogin`)` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/content/message/message.vue` | L62 | `const response = await request.get(` |
| `file:///Users/<USER>/Desktop/workspace/icb/itp-plugin/src/components/job-search.vue` | L60 | `const res = await request.post(`${config.api}/api/company/${userStore.companyId}/pipeline/job/require/search`, {` |

</details>

---

整理 /src 目录下所有 `request.post` 和 `request.get` 的调用点如下（按文件分类，含主要参数/接口路径，方便你全局排查和重构）：

---

**1. src/components/job-search.vue**
- `request.post`
  - `${config.api}/api/company/${userStore.companyId}/pipeline/job/require/search`

---

**2. src/components/duplicate-talents.vue**
- `request.get`
  - `${config.api}/api/talent/detail?talentId=13241486`

---

**3. src/components/talent-follow-up.vue**
- `request.get`
  - 具体接口参数见源码

---

**4. src/components/talent-job.vue**
- `request.get`
  - 具体接口参数见源码

---

**5. src/content/resume/ai/coze.js**
- `request.get`
  - `${config.api}/api/company/1/ai/coze/config`
- `request.post`
  - `${config.api}/api/company/1/ai/chatParams/get`
  - `${config.api}/api/company/1/ai/coze/appId/${cfg?.appId}/workflowId/${cfg?.onlineChatFlowId}`

---

**6. src/content/resume/auto-collection/index.ts**
- `request.get`
  - `${config.api}/api/company/1/ai/aiTopTalent/contact/config`

---

**7. src/content/message/handlers/linkedin-handler.ts**
- `request.get`
  - `apiUrl`（动态生成）
- `request.post`
  - `${config.api}/api/company/1/ai/chatParams/get`
  - `cozeUrl`（动态生成）

---

**8. src/content/message/handlers/zhipin-handler.ts**
- `request.get`
  - 注释掉的：`apiUrl`
- `request.post`
  - 具体接口参数见源码
  - `cozeUrl`（动态生成）

---

**9. src/content/message/handlers/liepin-handler.ts**
- `request.get`
  - `apiUrl`（动态生成）
- `request.post`
  - `${config.api}/api/company/1/ai/chatParams/get`
  - `cozeUrl`（动态生成）

---

**10. src/content/resume/main.vue**
- `request.post`
  - 多处，含：`${config.api}/api/talent/createOrUpdate`、`${config.api}/api/company/${userStore.companyId}/pipeline/addToJobRequirement` 等
- `request.get`
  - 多处，含：`${config.api}/api/company/${userStore.companyId}/pipeline/job/require/pipeline/by/talent/${talentId}`、`${config.api}/api/talent/checkDuplicate`、`${config.api}/api/company/checkLogin` 等

---

**11. src/utils/track.ts**
- `request.post`
  - `API_URL`（埋点/事件上报）

---

**12. src/content/message/message.vue**
- `request.get`
  - 具体接口参数见源码

---

**总结说明：**
- 绝大多数接口都通过 `config.api` 拼接，部分参数动态生成。
- 主要分布在 resume、message、components 相关业务文件。
- 你如需批量重构、mock、或统一拦截，建议直接在 `plugin-request.ts` 下手，或用 IDE 批量替换。

如需详细列出每一处的完整代码上下文或参数细节，随时说。

---

