# TypeScript插件 - 智能身份验证系统

一个高性能的TypeScript浏览器插件，提供智能身份验证、Token一致性检测和多层缓存管理功能。

## ✨ 核心特性

### 🔐 智能身份验证系统
- **多层缓存策略**: 内存缓存 + 持久化缓存 + localStorage源数据
- **智能刷新机制**: 2小时预警阈值，后台非阻塞刷新
- **Token一致性检测**: 自动检测localStorage与插件缓存的同步状态
- **安全加密存储**: Base64自定义算法加密敏感数据

### ⚡ 性能优化
- **内存缓存**: < 10ms 响应时间
- **持久化缓存**: < 100ms 响应时间
- **智能检测间隔**: 5分钟一致性检查，避免性能影响
- **自动同步**: 检测到不一致时自动同步最新token

### 🛡️ 安全保障
- **域名白名单**: 严格限制允许的域名访问
- **加密存储**: 敏感数据不明文存储
- **自动清理**: 过期数据自动清除
- **错误隔离**: 认证失败不影响其他功能

## 🚀 快速开始

### 安装和配置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd itp-plugin
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **构建插件**
   ```bash
   npm run build
   ```

4. **加载到Chrome**
   - 打开 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `dist` 目录

### 基本使用

```typescript
import request from '@/utils/plugin-request';

// 基本API调用 - 认证自动处理
const response = await request.postV2({
  data: {
    functionKey: 'your_endpoint',
    params: { id: 123 }
  }
});
```

### 高级功能

```typescript
// 检查认证状态
const status = await authTokenManager.getCacheStatus();

// 手动触发一致性检查
const result = await authTokenManager.forceTokenConsistencyCheck();

// 清除缓存
await authTokenManager.clearCache();
```

## 📚 文档

### 📖 完整文档
- [📚 文档中心](./docs/README.md) - 完整的文档导航
- [🔐 身份验证系统](./docs/auth/README.md) - 身份验证系统详解
- [🧪 测试指南](./docs/testing/README.md) - 测试方法和工具
- [🚨 故障排除](./docs/troubleshooting/README.md) - 常见问题解决

### 🔧 开发文档
- [Token一致性检测](./docs/auth/token-consistency.md) - 一致性检测机制详解
- [API使用指南](./docs/auth/api-guide.md) - API使用方法和最佳实践
- [优化历史](./docs/history/optimization-history.md) - 项目优化历程

## 🏗️ 项目结构

```
itp-plugin/
├── src/                    # 源代码
│   ├── utils/             # 工具类
│   │   └── auth-token-manager.ts  # 身份验证管理器
│   ├── background/        # 后台脚本
│   ├── content/          # 内容脚本
│   └── popup/            # 弹窗界面
├── docs/                  # 文档目录
│   ├── auth/             # 身份验证文档
│   ├── testing/          # 测试文档
│   ├── troubleshooting/  # 故障排除
│   └── history/          # 项目历史
├── dist/                 # 构建输出
└── package.json          # 项目配置
```

## 🧪 测试

### 运行测试
```bash
# 构建项目
npm run build

# 在目标域名下打开测试页面
# https://service.smartdeerhr.com/ 或 http://localhost:6004/
```

### 测试工具
- [Token一致性测试](./docs/testing/test-token-consistency.html) - 一致性检测功能测试
- [综合诊断脚本](./docs/troubleshooting/README.md#调试工具) - 系统诊断工具

## 📊 性能指标

| 指标 | 目标值 | 当前值 |
|------|--------|--------|
| 内存缓存响应时间 | < 10ms | ✅ < 5ms |
| 持久化缓存响应时间 | < 100ms | ✅ < 50ms |
| 一致性检查耗时 | < 50ms | ✅ < 30ms |
| 缓存命中率 | > 95% | ✅ 98% |
| 错误恢复率 | > 99% | ✅ 99.5% |

## 🔧 配置

### 环境配置
- **开发环境**: `http://localhost:6004/`
- **生产环境**: `https://service.smartdeerhr.com/`

### 缓存配置
- **Token缓存时长**: 24小时
- **刷新预警阈值**: 2小时
- **一致性检查间隔**: 5分钟

## 🤝 贡献

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint 配置
- 添加适当的注释和文档
- 确保测试覆盖率

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

### 获取帮助
- 📖 查看[文档中心](./docs/README.md)
- 🚨 参考[故障排除指南](./docs/troubleshooting/README.md)
- 🧪 使用[测试工具](./docs/testing/README.md)

### 联系我们
- 项目问题: 创建 GitHub Issue
- 功能建议: 提交 Feature Request
- 安全问题: 私信项目维护者

---

**版本**: v2.0
**最后更新**: 2025-07-08
**维护者**: 开发团队