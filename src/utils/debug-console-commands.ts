/**
 * 调试控制台命令
 * 在浏览器控制台中可以使用的调试命令
 */

import { authTokenManager } from './auth-token-manager';

// 将调试命令添加到全局对象，方便在控制台中使用
declare global {
  interface Window {
    debugAuthToken: {
      clearTokens: () => Promise<void>;
      getTokenStatus: () => Promise<any>;
      refreshToken: () => Promise<string>;
      clearAllTokens: () => Promise<void>;
    };
  }
}

/**
 * 初始化调试命令
 */
export function initDebugCommands() {
  if (typeof window !== 'undefined') {
    window.debugAuthToken = {
      /**
       * 清除认证令牌缓存
       */
      async clearTokens() {
        try {
          console.log('🧹 开始清除认证令牌缓存...');
          await authTokenManager.clearCache();
          console.log('✅ 认证令牌缓存已清除');
        } catch (error) {
          console.error('❌ 清除认证令牌缓存失败:', error);
        }
      },

      /**
       * 获取令牌状态信息
       */
      async getTokenStatus() {
        try {
          console.log('📊 获取认证令牌状态...');
          const status = await authTokenManager.getCacheStatus();
          console.log('📋 认证令牌状态:', status);
          
          if (status.timeUntilExpiry) {
            const hours = Math.floor(status.timeUntilExpiry / (1000 * 60 * 60));
            const minutes = Math.floor((status.timeUntilExpiry % (1000 * 60 * 60)) / (1000 * 60));
            console.log(`⏰ 令牌剩余有效时间: ${hours}小时${minutes}分钟`);
          }
          
          return status;
        } catch (error) {
          console.error('❌ 获取令牌状态失败:', error);
          return null;
        }
      },

      /**
       * 强制刷新令牌
       */
      async refreshToken() {
        try {
          console.log('🔄 开始强制刷新认证令牌...');
          // 先清除缓存
          await authTokenManager.clearCache();
          // 重新获取令牌
          const token = await authTokenManager.getAuthToken();
          console.log('✅ 令牌刷新成功:', token.substring(0, 20) + '...');
          return token;
        } catch (error) {
          console.error('❌ 刷新令牌失败:', error);
          throw error;
        }
      },

      /**
       * 清除所有认证令牌（包括localStorage）
       */
      async clearAllTokens() {
        try {
          console.log('🧹 开始清除所有认证令牌...');
          await authTokenManager.clearAllAuthTokens();
          console.log('✅ 所有认证令牌已清除');
        } catch (error) {
          console.error('❌ 清除所有认证令牌失败:', error);
        }
      }
    };

    // 在控制台中显示可用命令
    console.log(`
🔧 认证令牌调试命令已加载！

可用命令：
• debugAuthToken.clearTokens()     - 清除认证令牌缓存
• debugAuthToken.getTokenStatus()  - 获取令牌状态信息  
• debugAuthToken.refreshToken()    - 强制刷新令牌
• debugAuthToken.clearAllTokens()  - 清除所有认证令牌（包括localStorage）

示例用法：
await debugAuthToken.clearTokens();
await debugAuthToken.getTokenStatus();
    `);
  }
}

/**
 * 通过插件通信系统清除认证令牌
 */
export async function clearAuthTokensViaPlugin(): Promise<void> {
  try {
    // 发送消息到background script
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        callName: 'browse_plugin:clearAuthTokens',
        data: {}
      }, (response) => {
        if (response) {
          resolve(response);
        } else {
          reject(chrome.runtime.lastError);
        }
      });
    });
    
    console.log('✅ 通过插件系统清除认证令牌成功:', response);
  } catch (error) {
    console.error('❌ 通过插件系统清除认证令牌失败:', error);
    throw error;
  }
}
