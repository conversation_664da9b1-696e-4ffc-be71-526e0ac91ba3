
import {generateRandomString} from '@/utils/generateRandomString';
import { authTokenManager, AuthTokenError } from '@/utils/auth-token-manager';
import globalConfig from '@/config';

interface RequestConfig {
  data?: { [key: string]: any } | string,
  params?: { [key: string]: any }
  headers?: { [key: string]: any }
}

function sendMessage(message: any) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (response) {
        resolve(response)
      } else {
        reject(chrome.runtime.lastError)
      }
    })
  })
}

// 备用认证方案已移除 - 插件现在统一从指定域名获取authToken

async function sendRequest(method: 'post' | 'get', url: string, config: any): Promise<any> {
  const headerConfig = Object.assign({}, config.headers, {"Content-Type": "application/json"})
  const message = {
    callName: 'browse_plugin:request',
    data: {
      method: method,
      url: url,
      data: JSON.stringify(config.data),
      params: config.params,
      headers: headerConfig
    }
  }
  const result:any = await sendMessage(message)

  if (!result) {
    throw new Error('sendRequest error')
  } else if (result.status !== 'success') {
    const error = new Error(result.data.message)
    error.stack = result.data.stack
    throw error
  } else return result.data.data
}

async function get(url: string, config?: RequestConfig) {
  return await sendRequest('get', url, config || {})
}

async function sendRequestV2(_method: 'post' | 'get', config: any): Promise<any> {
  try {
    // let url = 'https://gs-api-02-test.smartdeer.tech/saas/v1/entity/u/icb_test/function/runtime';
    let url = globalConfig.APP_API_BASE_URL;
    url = url + '/' + generateRandomString();

    // 使用增强的身份验证管理器获取 authToken
    let authToken = '';

    try {
      authToken = await authTokenManager.getAuthToken();
      console.log('✅ 成功获取 authToken:', authToken.substring(0, 20) + '...');
    } catch (error) {
      if (error instanceof AuthTokenError) {
        // 提供用户友好的中文错误信息
        const errorMessage = `🔐 身份验证失败: ${error.message}`;
        const suggestion = error.suggestion ? `\n💡 建议: ${error.suggestion}` : '';

        console.error(errorMessage + suggestion);

        // 抛出更友好的错误信息
        throw new Error(`${errorMessage}${suggestion}`);
      } else {
        console.error('❌ 获取 authToken 时发生未知错误:', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`身份验证过程中发生错误: ${errorMessage}\n请检查网络连接并重试`);
      }
    }

    const headerConfig = Object.assign({}, config.headers, {
      "Content-Type": "application/json",
      "Authorization": authToken,
    });

    console.log('📤 发送请求使用的 authToken:', authToken ? authToken.substring(0, 20) + '...' : '(空)');

    // 验证必要的参数
    if (!config.data) {
      throw new Error('❌ 请求参数错误: config.data 是必需的');
    }

    const message = {
      callName: 'browse_plugin:request',
      data: {
        method: 'post', // 强制使用POST方法
        url,
        data: JSON.stringify({
          payload: JSON.stringify(config.data)
        }),
        headers: headerConfig
      }
    };

    console.log('📤 sendRequestV2: 发送请求到', url, '数据:', config.data);

    const result: any = await sendMessage(message);

    if (!result) {
      throw new Error('❌ 网络错误: 未收到后台脚本的响应\n请检查插件是否正常运行');
    }

    if (result.status !== 'success') {
      const errorMessage = result.data?.message || '未知错误';

      // 检查是否为认证相关错误
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') ||
          errorMessage.includes('authentication') || errorMessage.includes('token')) {
        throw new Error(`🔐 身份验证失败: ${errorMessage}\n💡 建议: 请重新登录或检查您的访问权限`);
      }

      // 检查是否为权限相关错误
      if (errorMessage.includes('403') || errorMessage.includes('Forbidden') ||
          errorMessage.includes('permission')) {
        throw new Error(`🚫 权限不足: ${errorMessage}\n💡 建议: 请联系管理员确认您的操作权限`);
      }

      // 检查是否为网络相关错误
      if (errorMessage.includes('timeout') || errorMessage.includes('network') ||
          errorMessage.includes('connection')) {
        throw new Error(`🌐 网络错误: ${errorMessage}\n💡 建议: 请检查网络连接并重试`);
      }

      // 通用错误
      const error = new Error(`❌ 请求失败: ${errorMessage}`);
      if (result.data?.stack) {
        error.stack = result.data.stack;
      }
      throw error;
    }

    if (result.data.data !== null) {
      console.log('✅ sendRequestV2: 请求成功', result.data);
      console.log('sendRequestV2: 返回数据:', result.data.data);
      return result.data.data;
    }

    console.log('✅ sendRequestV2: ❌ 请求失败', result.data.data);
    
    return null;

  } catch (error) {
    // 如果错误已经是我们格式化过的，直接抛出
    if (error instanceof Error && error.message.includes('💡')) {
      throw error;
    }

    // 否则包装为通用错误
    console.error('❌ sendRequestV2: 发生错误', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`请求处理失败: ${errorMessage}`);
  }
}

async function getV2(config?: RequestConfig) {
  return await sendRequestV2('get', config || {})
}

async function postV2(config?: RequestConfig) {
  return await sendRequestV2('post', config || {})
}

async function post(url: string, config: RequestConfig) {
  return await sendRequest('post', url, config || {})
}

/**
 * 获取认证状态信息（用于调试和监控）
 */
async function getAuthStatus() {
  try {
    const status = await authTokenManager.getCacheStatus();
    console.log('🔍 认证状态信息:', {
      hasMemoryCache: status.hasMemoryCache ? '✅' : '❌',
      hasStorageCache: status.hasStorageCache ? '✅' : '❌',
      isExpired: status.isExpired ? '⚠️ 已过期' : '✅ 有效',
      timeUntilExpiry: status.timeUntilExpiry
        ? `${Math.round(status.timeUntilExpiry / (1000 * 60 * 60))} 小时`
        : '无'
    });
    return status;
  } catch (error) {
    console.error('❌ 获取认证状态失败:', error);
    return null;
  }
}

/**
 * 手动刷新认证令牌
 */
async function refreshAuthToken() {
  try {
    console.log('🔄 手动刷新认证令牌...');
    await authTokenManager.clearCache();
    const token = await authTokenManager.getAuthToken();
    console.log('✅ 认证令牌刷新成功');
    return token;
  } catch (error) {
    console.error('❌ 刷新认证令牌失败:', error);
    throw error;
  }
}

/**
 * 清除所有认证缓存
 */
async function clearAuthCache() {
  try {
    console.log('🗑️ 清除认证缓存...');
    await authTokenManager.clearCache();
    console.log('✅ 认证缓存已清除');
  } catch (error) {
    console.error('❌ 清除认证缓存失败:', error);
    throw error;
  }
}

export default {
  get,
  post,
  getV2,
  postV2,
  // 认证相关的实用函数
  getAuthStatus,
  refreshAuthToken,
  clearAuthCache
}