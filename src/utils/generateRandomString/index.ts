/**
 * 生成一个指定长度的随机字符串。
 *
 * @param length 字符串的长度，默认为 128。
 * @returns 返回一个包含随机字符的字符串。
 */
export const generateRandomString = (length: number = 128): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // 字符集
  let result: string = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
