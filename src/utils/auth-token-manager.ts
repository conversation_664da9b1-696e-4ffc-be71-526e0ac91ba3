import Base64EncoderDecoder from '@/utils/base64EncoderDecoder';

// 常量定义
const TOKEN_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时
const TOKEN_REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // 2小时 - 在token过期前2小时开始尝试刷新
const TOKEN_CONSISTENCY_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟 - 一致性检查间隔
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAYS = [1000, 2000, 4000]; // 指数退避延迟
const ALLOWED_DOMAINS = ['https://service.smartdeerhr.com/', 'http://localhost:8006/']; // 支持多个端口

// 缓存键名
const CACHE_KEYS = {
  TOKEN: 'cached_auth_token',
  TIMESTAMP: 'token_timestamp',
  EXPIRY: 'token_expiry',
  LAST_CHECK: 'last_token_check',
  LAST_CONSISTENCY_CHECK: 'last_consistency_check',
  TOKEN_HASH: 'token_hash' // 用于快速比较token是否变化
} as const;

// 错误类型定义
export class AuthTokenError extends Error {
  constructor(message: string, public code: string, public suggestion?: string) {
    super(message);
    this.name = 'AuthTokenError';
  }
}

export class AuthTokenNotFoundError extends AuthTokenError {
  constructor() {
    super(
      '未找到有效的身份验证令牌',
      'TOKEN_NOT_FOUND',
      '请确保您已在 https://service.smartdeerhr.com/ 或 http://localhost:6004/ 或 http://localhost:8006/ 上正确登录'
    );
  }
}

export class AuthTokenExpiredError extends AuthTokenError {
  constructor() {
    super(
      '身份验证令牌已过期',
      'TOKEN_EXPIRED',
      '请重新登录以获取新的身份验证令牌'
    );
  }
}

// DomainNotAllowedError 已移除 - 插件现在可以在任何域名下使用

// 缓存数据接口
interface TokenCache {
  cached_auth_token?: string;
  token_timestamp?: number;
  token_expiry?: number;
  last_token_check?: number;
  last_consistency_check?: number;
  token_hash?: string;
}

/**
 * 身份验证令牌管理器
 * 负责管理 authToken 的获取、缓存和验证
 */
export class AuthTokenManager {
  private static instance: AuthTokenManager;
  private memoryCache: { token?: string; expiry?: number } = {};

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): AuthTokenManager {
    if (!AuthTokenManager.instance) {
      AuthTokenManager.instance = new AuthTokenManager();
    }
    return AuthTokenManager.instance;
  }

  /**
   * 获取身份验证令牌（主要入口方法）
   * 注意：插件可以在任何域名下使用，authToken始终从指定域名的localStorage获取
   */
  public async getAuthToken(): Promise<string> {
    try {
      const currentUrl = window.location.href;
      const isOnTargetDomain = ALLOWED_DOMAINS.some(domain => currentUrl.startsWith(domain));

      if (isOnTargetDomain) {
        // 在允许域名下：正常的一致性检查和刷新逻辑
        return await this.getAuthTokenForAllowedDomain();
      } else {
        // 在非允许域名下：只读模式，保护现有缓存
        return await this.getAuthTokenReadOnly();
      }

    } catch (error) {
      if (error instanceof AuthTokenError) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new AuthTokenError(
        `获取身份验证令牌时发生未知错误: ${errorMessage}`,
        'UNKNOWN_ERROR',
        '请检查网络连接并重试，如问题持续存在请联系技术支持'
      );
    }
  }

  /**
   * 在允许域名下获取身份验证令牌（完整功能）
   */
  private async getAuthTokenForAllowedDomain(): Promise<string> {
    console.log('🎯 在允许域名下获取 authToken，启用完整功能');

    // 检查token一致性
    await this.checkTokenConsistencyIfNeeded();

    // 检查内存缓存
    if (this.isMemoryCacheValid()) {
      console.log('✅ 使用内存缓存的 authToken');
      // 检查是否需要后台刷新
      this.scheduleTokenRefreshIfNeeded();
      return this.memoryCache.token!;
    }

    // 检查持久化缓存
    const cachedToken = await this.getCachedToken();
    if (cachedToken) {
      console.log('✅ 使用持久化缓存的 authToken');
      this.updateMemoryCache(cachedToken);
      // 检查是否需要后台刷新
      this.scheduleTokenRefreshIfNeeded();
      return cachedToken;
    }

    // 缓存无效，从指定域名的localStorage重新获取
    console.log('🔄 缓存无效，从指定域名重新获取 authToken');
    return await this.refreshAuthTokenWithRetry();
  }

  /**
   * 在非允许域名下获取身份验证令牌（只读模式）
   */
  private async getAuthTokenReadOnly(): Promise<string> {
    console.log('🔒 在非允许域名下获取 authToken，启用只读模式');

    // 检查内存缓存
    if (this.isMemoryCacheValid()) {
      console.log('✅ 使用内存缓存的 authToken (只读模式)');
      return this.memoryCache.token!;
    }

    // 检查持久化缓存（即使过期也使用）
    const cachedToken = await this.getCachedTokenReadOnly();
    if (cachedToken) {
      console.log('✅ 使用持久化缓存的 authToken (只读模式)');
      this.updateMemoryCache(cachedToken);
      return cachedToken;
    }

    // 没有任何缓存可用
    throw new AuthTokenNotFoundError();
  }

  // isOnAllowedDomain 方法已移除 - 插件现在可以在任何域名下使用

  /**
   * 检查内存缓存是否有效
   */
  private isMemoryCacheValid(): boolean {
    return !!(
      this.memoryCache.token &&
      this.memoryCache.expiry &&
      Date.now() < this.memoryCache.expiry
    );
  }

  /**
   * 更新内存缓存
   */
  private updateMemoryCache(token: string): void {
    this.memoryCache = {
      token,
      expiry: Date.now() + TOKEN_CACHE_DURATION
    };
  }

  /**
   * 获取缓存的令牌
   */
  private async getCachedToken(): Promise<string | null> {
    try {
      const cache = await this.getStorageCache();

      if (!cache.cached_auth_token || !cache.token_expiry) {
        console.log('📭 缓存中没有找到authToken或过期时间');
        return null;
      }

      // 检查是否过期
      if (Date.now() > cache.token_expiry) {
        console.log('⏰ 缓存的 authToken 已过期');
        await this.clearCache();
        return null;
      }

      console.log('✅ 从缓存中获取到有效的authToken');
      // 注意：这里返回的应该是已经解密的实际token值
      return cache.cached_auth_token;
    } catch (error) {
      console.error('❌ 获取缓存令牌失败:', error);
      return null;
    }
  }

  /**
   * 获取缓存的令牌（只读模式，即使过期也返回）
   */
  private async getCachedTokenReadOnly(): Promise<string | null> {
    try {
      const cache = await this.getStorageCache();

      if (!cache.cached_auth_token) {
        console.log('📭 缓存中没有找到authToken');
        return null;
      }

      // 在只读模式下，即使过期也返回token
      if (cache.token_expiry && Date.now() > cache.token_expiry) {
        console.log('⏰ 缓存的 authToken 已过期，但在只读模式下仍然使用');
      } else {
        console.log('✅ 从缓存中获取到authToken (只读模式)');
      }

      return cache.cached_auth_token;
    } catch (error) {
      console.error('❌ 获取缓存令牌失败 (只读模式):', error);
      return null;
    }
  }

  /**
   * 从 chrome.storage.local 获取缓存
   */
  private async getStorageCache(): Promise<TokenCache> {
    return new Promise((resolve) => {
      chrome.storage.local.get(Object.values(CACHE_KEYS), (result) => {
        resolve(result as TokenCache);
      });
    });
  }

  /**
   * 带重试机制的令牌刷新
   */
  private async refreshAuthTokenWithRetry(): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < MAX_RETRY_ATTEMPTS; attempt++) {
      try {
        if (attempt > 0) {
          console.log(`第 ${attempt + 1} 次尝试获取 authToken`);
          await this.delay(RETRY_DELAYS[attempt - 1]);
          await this.clearCache(); // 清除可能损坏的缓存
        }

        const token = await this.refreshAuthToken();
        console.log(`成功获取 authToken (尝试 ${attempt + 1}/${MAX_RETRY_ATTEMPTS})`);
        return token;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`第 ${attempt + 1} 次获取 authToken 失败:`, error);
        
        if (error instanceof AuthTokenNotFoundError && attempt === MAX_RETRY_ATTEMPTS - 1) {
          // 最后一次尝试仍然找不到 token，抛出更详细的错误
          throw new AuthTokenNotFoundError();
        }
      }
    }

    // 所有重试都失败了
    throw new AuthTokenError(
      `经过 ${MAX_RETRY_ATTEMPTS} 次尝试后仍无法获取身份验证令牌: ${lastError?.message}`,
      'MAX_RETRIES_EXCEEDED',
      '请检查您的登录状态，确保在正确的域名下操作，并重新登录'
    );
  }

  /**
   * 刷新身份验证令牌
   * 从指定域名的localStorage获取authToken，无论当前在哪个域名
   */
  private async refreshAuthToken(): Promise<string> {
    console.log('🔍 开始从指定域名搜索 authToken...');

    // 首先尝试从当前域名获取（如果当前就在目标域名下）
    const currentUrl = window.location.href;
    const isOnTargetDomain = ALLOWED_DOMAINS.some(domain => currentUrl.startsWith(domain));

    if (isOnTargetDomain) {
      console.log('✅ 当前在目标域名下，直接从localStorage获取');
      const token = await this.searchLocalStorageForToken();
      if (token) {
        return token;
      }
    }

    // 如果当前不在目标域名下，或者当前域名下没找到token
    // 尝试从chrome.storage中获取之前缓存的token
    console.log('🔄 尝试从插件存储中获取之前缓存的token...');
    const cachedToken = await this.getCachedToken();
    if (cachedToken) {
      console.log('✅ 从插件存储中找到缓存的token');
      this.updateMemoryCache(cachedToken);
      return cachedToken;
    }

    // 如果都没找到，抛出错误并提供指导
    throw new AuthTokenNotFoundError();
  }

  /**
   * 在localStorage中搜索authToken
   */
  private async searchLocalStorageForToken(): Promise<string | null> {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key) continue;

      try {
        const encryptedValue = localStorage.getItem(key);
        if (!encryptedValue) continue;

        // 尝试解密key
        const decryptedKey = Base64EncoderDecoder.decrypt(key);
        console.log(`🔍 检查 key: ${key}, 解密结果: ${decryptedKey}`);

        // 检查是否为 auth_token
        if (decryptedKey === 'auth_token') {
          console.log(`✅ 找到 authToken! key: ${key}`);

          // 尝试解密value
          let decryptedValue: string;
          try {
            decryptedValue = Base64EncoderDecoder.decrypt(encryptedValue);
            console.log(`🔓 解密后的值: ${decryptedValue}`);
          } catch (decryptError) {
            console.error(`❌ 解密令牌值失败: ${decryptError}`);
            continue;
          }

          // 尝试解析JSON
          let tokenData: any;
          try {
            tokenData = JSON.parse(decryptedValue);
            console.log(`📋 解析后的JSON:`, tokenData);
          } catch (parseError) {
            console.error(`❌ 解析JSON失败: ${parseError}`);
            // 如果不是JSON格式，直接使用解密后的值作为token
            tokenData = { value: decryptedValue };
          }

          // 提取实际的token值
          const actualToken = tokenData?.value || tokenData?.token || decryptedValue;

          if (!this.validateToken(actualToken)) {
            console.error(`❌ 提取的token格式无效:`, {
              token: actualToken?.substring(0, 50) + '...',
              type: typeof actualToken,
              length: actualToken?.length
            });
            continue;
          }

          console.log(`✅ 成功提取有效token: ${actualToken.substring(0, 20)}...`);

          // 存储解密后的实际token到缓存
          await this.setCacheWithExpiry(actualToken);
          this.updateMemoryCache(actualToken);

          return actualToken;
        }
      } catch (error) {
        // 解密失败，继续下一个 key
        console.log(`⚠️ 处理key ${key} 时出错: ${error}`);
        continue;
      }
    }

    console.log(`❌ 未在localStorage中找到有效的authToken`);
    return null;
  }

  /**
   * 设置缓存并添加过期时间
   */
  private async setCacheWithExpiry(token: string): Promise<void> {
    const now = Date.now();
    const expiry = now + TOKEN_CACHE_DURATION;
    const tokenHash = this.generateTokenHash(token);

    const cacheData = {
      [CACHE_KEYS.TOKEN]: token,
      [CACHE_KEYS.TIMESTAMP]: now,
      [CACHE_KEYS.EXPIRY]: expiry,
      [CACHE_KEYS.LAST_CHECK]: now,
      [CACHE_KEYS.TOKEN_HASH]: tokenHash
    };

    return new Promise((resolve) => {
      chrome.storage.local.set(cacheData, () => {
        console.log('authToken 已存储到插件缓存系统，有效期24小时');
        resolve();
      });
    });
  }

  /**
   * 清除所有缓存
   */
  public async clearCache(): Promise<void> {
    this.memoryCache = {};

    return new Promise((resolve) => {
      chrome.storage.local.remove(Object.values(CACHE_KEYS), () => {
        console.log('🧹 authToken 缓存已清除');
        resolve();
      });
    });
  }

  /**
   * 清除认证令牌（包括localStorage中的原始数据）
   * 用于调试和重置认证状态
   */
  public async clearAllAuthTokens(): Promise<void> {
    console.log('🧹 开始清除所有认证令牌...');

    // 清除插件缓存
    await this.clearCache();

    // 清除localStorage中的认证令牌（如果当前在目标域名下）
    const currentUrl = window.location.href;
    const isOnTargetDomain = ALLOWED_DOMAINS.some(domain => currentUrl.startsWith(domain));

    if (isOnTargetDomain) {
      console.log('🎯 当前在目标域名下，清除localStorage中的认证令牌');

      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (!key) continue;

        try {
          const decryptedKey = Base64EncoderDecoder.decrypt(key);
          if (decryptedKey === 'auth_token') {
            keysToRemove.push(key);
          }
        } catch (error) {
          // 忽略解密失败的key
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🗑️ 已删除localStorage中的认证令牌: ${key}`);
      });

      console.log(`✅ 共清除了 ${keysToRemove.length} 个认证令牌`);
    } else {
      console.log('ℹ️ 当前不在目标域名下，仅清除插件缓存');
    }

    console.log('✅ 认证令牌清除完成');
  }

  /**
   * 验证令牌格式是否有效
   */
  private validateToken(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // 基本长度检查（authToken通常比较长）
    if (token.length < 10) {
      return false;
    }

    // 检查是否包含明显的损坏标识（但不包括****，因为这可能是正常的token格式）
    if (token.includes('undefined') || token.includes('null') || token === '') {
      return false;
    }

    // 检查是否为纯空白字符
    if (token.trim() === '') {
      return false;
    }

    return true;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取缓存状态信息（用于调试）
   */
  public async getCacheStatus(): Promise<{
    hasMemoryCache: boolean;
    hasStorageCache: boolean;
    isExpired: boolean;
    timeUntilExpiry?: number;
    needsRefresh?: boolean;
    lastConsistencyCheck?: number;
    needsConsistencyCheck?: boolean;
  }> {
    const cache = await this.getStorageCache();
    const hasStorageCache = !!(cache.cached_auth_token && cache.token_expiry);
    const isExpired = hasStorageCache ? Date.now() > cache.token_expiry! : true;
    const timeUntilExpiry = hasStorageCache && !isExpired
      ? cache.token_expiry! - Date.now()
      : undefined;
    const needsRefresh = hasStorageCache && !isExpired
      ? (cache.token_expiry! - Date.now()) < TOKEN_REFRESH_THRESHOLD
      : false;

    const needsConsistencyCheck = cache.last_consistency_check
      ? (Date.now() - cache.last_consistency_check) >= TOKEN_CONSISTENCY_CHECK_INTERVAL
      : true;

    return {
      hasMemoryCache: this.isMemoryCacheValid(),
      hasStorageCache,
      isExpired,
      timeUntilExpiry,
      needsRefresh,
      lastConsistencyCheck: cache.last_consistency_check,
      needsConsistencyCheck
    };
  }

  /**
   * 检查token是否需要刷新（距离过期时间小于阈值）
   */
  private async shouldRefreshToken(): Promise<boolean> {
    const cache = await this.getStorageCache();
    if (!cache.token_expiry) {
      return false;
    }

    const timeUntilExpiry = cache.token_expiry - Date.now();
    return timeUntilExpiry > 0 && timeUntilExpiry < TOKEN_REFRESH_THRESHOLD;
  }

  /**
   * 如果需要，安排后台token刷新（仅在允许域名下执行）
   */
  private scheduleTokenRefreshIfNeeded(): void {
    // 检查当前是否在允许域名下
    const currentUrl = window.location.href;
    const isOnTargetDomain = ALLOWED_DOMAINS.some(domain => currentUrl.startsWith(domain));

    if (!isOnTargetDomain) {
      console.log('🔒 当前在非允许域名下，跳过后台token刷新');
      return;
    }

    // 使用异步方式检查，避免阻塞主流程
    this.shouldRefreshToken().then(needsRefresh => {
      if (needsRefresh) {
        console.log('🔄 Token即将过期，开始后台刷新...');
        this.refreshTokenInBackground();
      }
    }).catch(error => {
      console.warn('⚠️ 检查token刷新需求时出错:', error);
    });
  }

  /**
   * 后台刷新token（不阻塞当前请求）
   */
  private async refreshTokenInBackground(): Promise<void> {
    try {
      console.log('🔄 开始后台刷新token...');
      const newToken = await this.refreshAuthToken();
      console.log('✅ 后台token刷新成功');

      // 更新缓存
      await this.setCacheWithExpiry(newToken);
      this.updateMemoryCache(newToken);

    } catch (error) {
      console.warn('⚠️ 后台token刷新失败，将在下次请求时重试:', error);
      // 后台刷新失败不影响当前请求，只记录日志
    }
  }

  /**
   * 检查token一致性（如果需要）
   * 智能检测localStorage中的token与插件缓存是否一致
   */
  private async checkTokenConsistencyIfNeeded(): Promise<void> {
    try {
      const cache = await this.getStorageCache();
      const now = Date.now();

      // 检查是否需要进行一致性检查
      if (cache.last_consistency_check &&
          (now - cache.last_consistency_check) < TOKEN_CONSISTENCY_CHECK_INTERVAL) {
        // 距离上次检查时间不足，跳过检查
        return;
      }

      console.log('🔍 开始检查token一致性...');

      // 获取localStorage中的当前token
      const localStorageToken = await this.getCurrentLocalStorageToken();
      if (!localStorageToken) {
        console.log('📭 localStorage中未找到token，跳过一致性检查');
        await this.updateConsistencyCheckTime();
        return;
      }

      // 比较token是否一致
      const isConsistent = await this.compareTokens(localStorageToken, cache.cached_auth_token);

      if (!isConsistent) {
        console.log('⚠️ 检测到token不一致，开始同步...');
        await this.syncTokenFromLocalStorage(localStorageToken);
      } else {
        console.log('✅ Token一致性检查通过');
      }

      // 更新检查时间
      await this.updateConsistencyCheckTime();

    } catch (error) {
      console.warn('⚠️ Token一致性检查失败:', error);
      // 一致性检查失败不影响主流程
    }
  }

  /**
   * 获取localStorage中的当前token（不更新缓存）
   */
  private async getCurrentLocalStorageToken(): Promise<string | null> {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key) continue;

      try {
        const encryptedValue = localStorage.getItem(key);
        if (!encryptedValue) continue;

        // 尝试解密key
        const decryptedKey = Base64EncoderDecoder.decrypt(key);

        // 检查是否为 auth_token
        if (decryptedKey === 'auth_token') {
          // 尝试解密value
          const decryptedValue = Base64EncoderDecoder.decrypt(encryptedValue);

          // 尝试解析JSON
          let tokenData: any;
          try {
            tokenData = JSON.parse(decryptedValue);
          } catch (parseError) {
            tokenData = { value: decryptedValue };
          }

          // 提取实际的token值
          const actualToken = tokenData?.value || tokenData?.token || decryptedValue;

          if (this.validateToken(actualToken)) {
            return actualToken;
          }
        }
      } catch (error) {
        // 解密失败，继续下一个 key
        continue;
      }
    }

    return null;
  }

  /**
   * 比较两个token是否一致
   */
  private async compareTokens(localToken: string, cachedToken?: string): Promise<boolean> {
    if (!cachedToken) {
      return false;
    }

    // 直接比较token字符串
    if (localToken === cachedToken) {
      return true;
    }

    // 使用hash比较（如果有缓存的hash）
    const cache = await this.getStorageCache();
    if (cache.token_hash) {
      const localTokenHash = this.generateTokenHash(localToken);
      return localTokenHash === cache.token_hash;
    }

    return false;
  }

  /**
   * 从localStorage同步token到插件缓存
   */
  private async syncTokenFromLocalStorage(newToken: string): Promise<void> {
    console.log('🔄 开始同步token到插件缓存...');

    try {
      // 清除旧缓存
      await this.clearCache();

      // 存储新token
      await this.setCacheWithExpiry(newToken);

      // 更新内存缓存
      this.updateMemoryCache(newToken);

      console.log('✅ Token同步完成');

    } catch (error) {
      console.error('❌ Token同步失败:', error);
      throw error;
    }
  }

  /**
   * 生成token的hash值用于快速比较
   */
  private generateTokenHash(token: string): string {
    // 简单的hash算法，用于快速比较
    let hash = 0;
    for (let i = 0; i < token.length; i++) {
      const char = token.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 更新一致性检查时间
   */
  private async updateConsistencyCheckTime(): Promise<void> {
    const now = Date.now();
    return new Promise((resolve) => {
      chrome.storage.local.set({
        [CACHE_KEYS.LAST_CONSISTENCY_CHECK]: now
      }, () => {
        resolve();
      });
    });
  }

  /**
   * 手动触发token一致性检查和同步
   * 公共方法，可用于调试或特殊场景
   */
  public async forceTokenConsistencyCheck(): Promise<{
    wasInconsistent: boolean;
    syncPerformed: boolean;
    error?: string;
  }> {
    try {
      console.log('🔍 手动触发token一致性检查...');

      // 获取localStorage中的当前token
      const localStorageToken = await this.getCurrentLocalStorageToken();
      if (!localStorageToken) {
        return {
          wasInconsistent: false,
          syncPerformed: false,
          error: 'localStorage中未找到token'
        };
      }

      // 获取缓存中的token
      const cache = await this.getStorageCache();

      // 比较token是否一致
      const isConsistent = await this.compareTokens(localStorageToken, cache.cached_auth_token);

      if (!isConsistent) {
        console.log('⚠️ 检测到token不一致，执行同步...');
        await this.syncTokenFromLocalStorage(localStorageToken);
        await this.updateConsistencyCheckTime();

        return {
          wasInconsistent: true,
          syncPerformed: true
        };
      } else {
        console.log('✅ Token一致性检查通过，无需同步');
        await this.updateConsistencyCheckTime();

        return {
          wasInconsistent: false,
          syncPerformed: false
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 手动一致性检查失败:', error);

      return {
        wasInconsistent: false,
        syncPerformed: false,
        error: errorMessage
      };
    }
  }
}

// 导出单例实例
export const authTokenManager = AuthTokenManager.getInstance();
