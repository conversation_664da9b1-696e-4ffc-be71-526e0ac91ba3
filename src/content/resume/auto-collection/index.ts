// 加载配置接口
import request from '@/utils/plugin-request'
import config from '@/config'
import { useUserStore } from '@/content/user-store'
import { mainLoop as bossMainLoop } from './boss'
import { mainLoop as liepinMainLoop } from './liepin'
import { mainLoop as maimai<PERSON>ain<PERSON>oop } from './maimai'
import { isZhipin, isL<PERSON>pin, isLinkedin, isMaimai } from '../constants'
import { getCozeConfig, getCozeChatParams } from '../ai/coze'

// 加载配置
export async function loadConfig() {
  try {
    return await request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_get_contact_config',
        params: {}
      }
    })
  } catch (e) {
    console.log(`e: `, e)
    throw e
  }
}

function preCheckConfig(configData: any): boolean {
  const sessionKey = 'autoCollectionConfig';
  const sessionDataRaw = sessionStorage.getItem(sessionKey);
  if (sessionDataRaw) {
    try {
      const sessionData = JSON.parse(sessionDataRaw);
      if (JSON.stringify(sessionData) === JSON.stringify(configData)) {
        // 数据一致，不继续往下执行
        return false;
      } else {
        // 数据不一致，覆盖
        sessionStorage.setItem(sessionKey, JSON.stringify(configData));
        return true;
      }
    } catch (e) {
      // 解析失败，覆盖
      sessionStorage.setItem(sessionKey, JSON.stringify(configData));
      return true;
    }
  } else {
    // 没有数据，写入
    sessionStorage.setItem(sessionKey, JSON.stringify(configData));
    return true;
  }
}

export function initCollectionResume() {
  console.log('initCollectionResume')
  loadConfig().then((res) => {
    // {
    //   "maimai": {
    //     "autoOpen": 1,
    //     "autoRequestResume": true,
    //     "contactContent": "亲爱的候选人您好，看到您的简介后感觉您跟我们公司非常匹配，方便交换一下联系方式我们有后续的沟通吗？",
    //     "dailyChatLimit": 20,
    //     "isAutoContact": 1,
    //     "messageReplyFrequency": 300,
    //     "selectedDays": [],
    //     "timeRange": ["00:00", "06:00"],
    //     "timeRangeType": "daily",
    //     "useBotChat": true
    //   }
    // }
    console.log('initCollectionResume: ', res);

    // 猎聘 不使用 preCheckConfig，因为每次会刷新
    if (!isLiepin) {
      if (!preCheckConfig(res?.data)) return;
    }
    
    let data = res?.data;
    // debugger;
    if (data?.maimai?.useBotChat === true && isMaimai) {
      console.log('initCollectionResume: maimai', data.maimai);
      maimaiMainLoop(data.maimai);
    } else if (data?.liepin?.useBotChat === true && isLiepin) {
      console.log('initCollectionResume: liepin', data.liepin);
      liepinMainLoop(data.liepin);
    } else if (data?.boss?.useBotChat === true && isZhipin) {
      console.log('initCollectionResume: boss', data.boss);
      bossMainLoop(data.boss);
    }
  }).catch((err) => {
    console.log('initCollectionResume: ', err);
  })
}