
import request from '@/utils/plugin-request'
import config from '@/config'

export const getCozeConfig = () => {
  try {
    return request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_config',
        params: {}
      }
    })
  } catch (e) {
    console.log(`e: `, e)
    throw e
  }
}

// 获取聊天参数
export const getCozeChatParams = ({ site, positionName, userInput }) => {
  try {
    return request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_get_chat_params',
        params: {
          site,
          positionName,
          action: '2', // 聊天统一为2
          userInput // 必填，多条用 \n 分隔
        }
      }
    })
  } catch (e) {
    console.log(`e: `, e)
    throw e
  }
}


export const getCozeWorkflow = (data, cfg) => {
  try {
    return request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_execute_workflow',
        params: {
          workflowData: data,
          appId: cfg?.appId,
          workflowId: cfg?.onlineChatFlowId
        }
      }
    })
  } catch (e) {
    console.log(`e: `, e)
    throw e
  }
}





