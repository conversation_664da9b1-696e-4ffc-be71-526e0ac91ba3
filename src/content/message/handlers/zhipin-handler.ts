import type {
  Message<PERSON><PERSON><PERSON>,
  UnreadMessageInfo,
} from "../types/message-handler";
import { createBaseMessageHandler } from "./base-message-handler";
import { IntervalPresets, addRandomDelay } from "../utils/randomization";
import request from "@/utils/plugin-request";
import config from "@/config";

// Global variable to store coze configuration
let cozeConfig: any = null;

function createZhipinConfig() {
  return {
    websiteName: "Zhipin",
    urlPattern: "zhipin.com/web/geek/chat",
    checkInterval: 30000, // This will be overridden by getNextInterval
  };
}

// Helper function to set coze configuration
function setCozeConfig(configData: any): void {
  cozeConfig = configData;
  console.log("Coze config set in zhipin handler:", cozeConfig);
}

// Helper function to get coze configuration
function getCozeConfig(): any {
  return cozeConfig;
}

// Function to extract latest user messages from conversation
function extractLatestUserMessages(
  conversation: Array<{ from: string; sendTime: string; message: string }>
): string {
  // Find the latest user messages (consecutive user messages from the end)
  const userMessages: string[] = [];

  // Start from the end and collect consecutive user messages
  for (let i = conversation.length - 1; i >= 0; i--) {
    const message = conversation[i];
    if (message.from === "user") {
      userMessages.unshift(message.message); // Add to beginning to maintain order
    } else {
      // Stop when we encounter a non-user message
      break;
    }
  }

  return userMessages.join("\n");
}

async function checkForNewMessages(): Promise<UnreadMessageInfo[]> {
  console.log("Checking for new messages on Zhipin...");

  // Query all conversation list items using Zhipin selectors
  const conversationItems = document.querySelectorAll(".geek-item-wrap");
  console.log(`Found ${conversationItems.length} conversation items`);

  if (conversationItems.length === 0) {
    console.log("No conversation items found");
    return [];
  }

  const unreadMessages: UnreadMessageInfo[] = [];
  const unreadMessageElements: Element[] = [];

  // Check each conversation item for unread messages
  conversationItems.forEach((item, index) => {
    // Look for unread message badge
    const unreadBadge = item.querySelector('.badge-count.badge-count-common-less span');
    
    if (unreadBadge) {
      let unreadCount = 1;
      const badgeText = unreadBadge.textContent?.trim() || "1";
      const parsedCount = parseInt(badgeText, 10);
      unreadCount = isNaN(parsedCount) ? 1 : parsedCount;

      console.log(
        `Found unread message indicator at index ${index} with count: ${unreadCount}`
      );

      // Get the clickable contact element
      const contactElement = item.querySelector(".geek-item") as HTMLElement;

      if (contactElement) {
        // Store the original .geek-item-wrap element for checkAllContactsForCID
        unreadMessageElements.push(item);
        
        // Store the UnreadMessageInfo for the interface requirement
        unreadMessages.push({
          count: unreadCount,
          contactElement,
          index,
        });
      }
    }
  });

  // can we just use unreadMessageElements.length?
  // if no unread messages found, check each contact individually for testing
  if (unreadMessageElements.length === 0 && conversationItems.length > 0) {
    console.log(
      "No unread indicators found, checking each contact individually for profile extraction..."
    );
    await checkAllContactsForCID(conversationItems);
  }

  return unreadMessages;
}

async function checkAllContactsForCID(
  contactItems: NodeListOf<Element>
): Promise<void> {
  for (let i = 0; i < contactItems.length; i++) {
    const item = contactItems[i];
    const contactElement = item.querySelector(".geek-item") as HTMLElement;

    if (!contactElement) continue;

    console.log(`Checking Zhipin contact ${i + 1}/${contactItems.length} for profile data...`);

    try {
      // Add delay before clicking
      await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

      // Click the contact
      contactElement.click();

      // Wait for chat to load
      await addRandomDelay({ minDelay: 1500, maxDelay: 2500 });

      // Extract profile data from Zhipin conversation
      await extractCIDFromProfile(i + 1);
    } catch (error) {
      console.error(`Error checking Zhipin contact ${i + 1}:`, error);
      continue;
    }
  }

  console.log("Finished checking all Zhipin contacts for profile data");
}

async function extractCIDFromProfile(
  contactNumber: number
): Promise<void> {
}

async function extractConversation(
  contactNumber: number,
  cid: string,
  data: any
): Promise<void> {
  // TODO: Implement Zhipin-specific conversation extraction
  console.log(`Contact ${contactNumber}: Zhipin conversation extraction not yet implemented`);
  
  // Placeholder structure
  const conversation: Array<{
    from: string;
    sendTime: string;
    message: string;
  }> = [];

  // TODO: Find Zhipin chat interface and extract messages
  // const chatContainer = document.querySelector('.chat-messages'); // TODO: Update selector
  // if (!chatContainer) {
  //   console.log(`Contact ${contactNumber}: Not in chat interface`);
  //   return;
  // }

  // TODO: Extract messages from Zhipin chat interface
  // const messageElements = chatContainer.querySelectorAll('.message-item'); // TODO: Update selector

  if (conversation.length > 0) {
    console.log(`Contact ${contactNumber} (CID: ${cid}) - Conversation:`);
    console.log(JSON.stringify(conversation, null, 2));

    const cozeConfigData = getCozeConfig();

    if (cozeConfigData && data) {
      // Extract latest user messages for userInput field
      const userInput = extractLatestUserMessages(conversation);

      const response = await request.postV2({
        data: {
          functionKey: 'rcn_plugin_ai_get_chat_params',
          params: {
            site: 3, // TODO: Confirm site ID for Zhipin
            jobRequirementId: data.id,
            positionName: data.processName,
            action: 2,
            chatContext: JSON.stringify(conversation),
            userInput: userInput,
          }
        }
      });

      await replyToUser(response.data, contactNumber);
    }
  } else {
    console.log(`Contact ${contactNumber}: No conversation messages found`);
  }
}

async function replyToUser(response: any, contactNumber: number): Promise<void> {
  console.log(`Contact ${contactNumber}: Replying to Zhipin user...`);

  const cozeConfigData = getCozeConfig();
  
  if (!cozeConfigData) {
    console.log("No coze config available for Zhipin reply");
    return;
  }

  try {
    const cozeResponse = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_execute_workflow',
        params: {
          workflowData: {
            params: {
              ...response,
            },
          },
          appId: cozeConfigData.appId,
          workflowId: cozeConfigData.onlineChatFlowId
        }
      }
    });
    console.log(`res: `, cozeResponse);

    console.log(`Contact ${contactNumber}: Coze API Response:`, cozeResponse);

  // TODO: Implement Zhipin-specific message sending
  // Extract the message from cozeResponse and send it through Zhipin's interface
  if (cozeResponse?.code === 0 && cozeResponse?.data) {
    try {
      const responseData = JSON.parse(cozeResponse.data);
      const messageToSend = responseData.output;
      
      if (messageToSend) {
        console.log(`Contact ${contactNumber}: Extracted message:`, messageToSend);
        
        // TODO: Find Zhipin message input box and send message
        // const messageInput = document.querySelector('.message-input'); // TODO: Update selector
        // if (messageInput) {
        //   // Insert message logic here
        //   console.log(`Contact ${contactNumber}: Message inserted into Zhipin chat box`);
        // }
      }
    } catch (parseError) {
      console.log(`Contact ${contactNumber}: Error parsing coze response data:`, parseError);
    }
  }
  } catch (e) {
    console.log(`e: `, e)
  }
}

async function handleUnreadMessage(
  messageInfo: UnreadMessageInfo
): Promise<void> {
  console.log(
    `Handling unread Zhipin message at index ${messageInfo.index} with count: ${messageInfo.count}`
  );

  try {
    // Add human-like delay before clicking
    await addRandomDelay();

    // Click the contact element
    messageInfo.contactElement.click();
    console.log("Successfully clicked Zhipin contact");

    // Wait for chat window to load
    await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

    // Check if we're in a chat window and process received messages
    await processReceivedMessages();

    // Update statistics
    await updateMessageStats();
  } catch (error) {
    console.error("Error handling Zhipin message:", error);
    throw error;
  }
}

async function processReceivedMessages(): Promise<void> {
  console.log("Processing received messages in Zhipin chat window...");

  // TODO: Implement Zhipin-specific message processing
  // const chatContainer = document.querySelector('.chat-container'); // TODO: Update selector
  // if (!chatContainer) {
  //   console.log("Not in chat interface, skipping message processing");
  //   return;
  // }

  console.log("Zhipin message processing not yet implemented");
}

async function updateMessageStats(): Promise<void> {
  try {
    const today = new Date().toDateString();
    const statsKey = "zhipin-message-stats";
    const existingStats = localStorage.getItem(statsKey);

    let stats = {
      todayProcessed: 0,
      weekProcessed: 0,
      lastProcessed: today,
    };

    if (existingStats) {
      const parsed = JSON.parse(existingStats);
      stats = { ...stats, ...parsed };

      // Reset daily count if it's a new day
      if (stats.lastProcessed !== today) {
        stats.todayProcessed = 0;
        stats.lastProcessed = today;
      }
    }

    stats.todayProcessed++;
    stats.weekProcessed++;

    localStorage.setItem(statsKey, JSON.stringify(stats));
    console.log("Updated Zhipin message statistics:", stats);
  } catch (error) {
    console.error("Error updating Zhipin message stats:", error);
  }
}

export function createZhipinHandler(configData?: any): MessageHandler {
  // Set the coze configuration if provided
  if (configData) {
    setCozeConfig(configData);
  }

  const config = createZhipinConfig();

  const handlerFunctions = {
    checkForNewMessages,
    handleUnreadMessage,
    getNextInterval: IntervalPresets.moderate, // Use moderate randomization preset
  };

  return createBaseMessageHandler(config, handlerFunctions);
} 