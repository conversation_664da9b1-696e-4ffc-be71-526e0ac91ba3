import type {
  Message<PERSON><PERSON><PERSON>,
  UnreadMessageInfo,
} from "../types/message-handler";
import { createBaseMessageHandler } from "./base-message-handler";
import { IntervalPresets, addRandomDelay } from "../utils/randomization";
import request from "@/utils/plugin-request";
import config from "@/config";

// Global variable to store coze configuration
let cozeConfig: any = null;

function createLinkedInConfig() {
  return {
    websiteName: "LinkedIn",
    urlPattern: "linkedin.com/messaging/",
    checkInterval: 30000, // This will be overridden by getNextInterval
  };
}

// Helper function to set coze configuration
function setCozeConfig(configData: any): void {
  cozeConfig = configData;
  console.log("Coze config set in linkedin handler:", cozeConfig);
}

// Helper function to get coze configuration
function getCozeConfig(): any {
  return cozeConfig;
}

// Function to extract latest user messages from conversation
function extractLatestUserMessages(
  conversation: Array<{ from: string; sendTime: string; message: string }>
): string {
  // Find the latest user messages (consecutive user messages from the end)
  const userMessages: string[] = [];

  // Start from the end and collect consecutive user messages
  for (let i = conversation.length - 1; i >= 0; i--) {
    const message = conversation[i];
    if (message.from === "user") {
      userMessages.unshift(message.message); // Add to beginning to maintain order
    } else {
      // Stop when we encounter a non-user message
      break;
    }
  }

  return userMessages.join("\n");
}

async function checkForNewMessages(): Promise<UnreadMessageInfo[]> {
  console.log("Checking for new messages on LinkedIn...");

  // Query all conversation list items
  const conversationItems = document.querySelectorAll("li.msg-conversation-listitem");
  console.log(`Found ${conversationItems.length} conversation items`);

  if (conversationItems.length === 0) {
    console.log("No conversation items found");
    return [];
  }

  const unreadMessages: UnreadMessageInfo[] = [];
  const unreadMessageElements: Element[] = [];

  // Check each conversation item for unread messages
  conversationItems.forEach((item, index) => {
    // Look for unread indicators - LinkedIn typically uses badges or bold text for unread
    // Check for unread badge/indicator elements
    const unreadBadge = item.querySelector('.msg-conversation-card__unread-badge, .msg-conversation-card__badge, [data-test-unread-indicator]');
    
    // Also check if the conversation title is bold (another indicator of unread)
    const titleElement = item.querySelector('.msg-conversation-card__participant-names');
    const isBold = titleElement && window.getComputedStyle(titleElement).fontWeight === 'bold';
    
    // Check for active conversation (might indicate new activity)
    const isActive = item.querySelector('.msg-conversation-listitem__link--active');
    
    if (unreadBadge || isBold || isActive) {
      // Try to extract unread count from badge text, default to 1 if not found
      let unreadCount = 1;
      if (unreadBadge) {
        const badgeText = unreadBadge.textContent?.trim() || "1";
        const parsedCount = parseInt(badgeText, 10);
        unreadCount = isNaN(parsedCount) ? 1 : parsedCount;
      }

      console.log(
        `Found unread message indicator at index ${index} with count: ${unreadCount}`
      );

      // Find the clickable contact element
      const contactElement = item.querySelector(
        ".msg-conversation-listitem__link"
      ) as HTMLElement;

      if (contactElement) {
        // Store the original li.msg-conversation-listitem element for checkAllContactsForCID
        unreadMessageElements.push(item);
        
        // Store the UnreadMessageInfo for the interface requirement
        unreadMessages.push({
          count: unreadCount,
          contactElement,
          index,
        });
      }
    }
  });

  // If no unread messages found, check each contact individually for testing
  if (unreadMessageElements.length === 0 && conversationItems.length > 0) {
    console.log(
      "No unread indicators found, checking each contact individually for profile extraction..."
    );
    await checkAllContactsForCID(conversationItems);
  }

  return unreadMessages;
}

async function checkAllContactsForCID(
  contactItems: NodeListOf<Element>
): Promise<void> {
  for (let i = 0; i < contactItems.length; i++) {
    const item = contactItems[i];
    const contactElement = item.querySelector(
      ".msg-conversation-listitem__link"
    ) as HTMLElement;

    if (!contactElement) continue;

    console.log(`Checking LinkedIn contact ${i + 1}/${contactItems.length} for profile data...`);

    try {
      // Add delay before clicking
      await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

      // Click the contact
      contactElement.click();

      // Wait for chat to load
      await addRandomDelay({ minDelay: 1500, maxDelay: 2500 });

      // Extract profile data from LinkedIn conversation
      await extractCIDFromProfile(i + 1);
    } catch (error) {
      console.error(`Error checking LinkedIn contact ${i + 1}:`, error);
      continue;
    }
  }

  console.log("Finished checking all LinkedIn contacts for profile data");
}

async function extractCIDFromProfile(
  contactNumber: number
): Promise<void> {
  // Look for all code elements with bpr-guid to see what's available
  const allCodeElements = document.querySelectorAll('code[id*="bpr-guid"]');
  console.log(`Contact ${contactNumber}: Found ${allCodeElements.length} code elements with bpr-guid`);
  
  // Log all code element IDs and preview their content
  allCodeElements.forEach((element, index) => {
    const id = element.id;
    const content = element.textContent?.substring(0, 200) + "...";
  });

  // Look for the hidden code element containing JSON data
  const codeElement = document.querySelector(
    'code[id*="bpr-guid"]'
  ) as HTMLElement;

  if (!codeElement) {
    console.log(`Contact ${contactNumber}: JSON data element not found`);
    return;
  }

  // Get the JSON content
  const jsonContent = codeElement.textContent;

  if (!jsonContent) {
    console.log(`Contact ${contactNumber}: JSON content not found`);
    return;
  }

  // Use regex to find urn:li:member pattern directly in the text content
  const memberUrnPattern = /urn:li:member:\d+/g;
  let memberUrns: string[] = [];
  
  // Check all code elements for member URNs
  allCodeElements.forEach((element, index) => {
    const content = element.textContent || '';
    const matches = content.match(memberUrnPattern);
    if (matches) {
      memberUrns = memberUrns.concat(matches);
    }
  });

  // Remove duplicates and use the first one found
  const uniqueUrns = [...new Set(memberUrns)];
  console.log(`Contact ${contactNumber}: All unique member URNs found:`, uniqueUrns);

  if (uniqueUrns.length > 0) {
    const objectUrn = uniqueUrns[0];
    console.log(`Contact ${contactNumber}: Using objectUrn = ${objectUrn}`);

    const response = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_resume_match_project',
        params: {
          resumeId: objectUrn,
          site: 4
        }
      }
    });

    console.log(`Contact ${contactNumber}: API Response:`, response);
    console.log(`Contact ${contactNumber}: API Response:`, response.data);

    // After getting objectUrn, extract the conversation
    await extractConversation(contactNumber, objectUrn, response.data);
  } else {
    console.log(`Contact ${contactNumber}: No member URNs found in any code elements`);
  }
}

async function extractConversation(
  contactNumber: number,
  cid: string,
  data: any
): Promise<void> {
  // Check if we're in the chat interface
  const messageList = document.querySelector(".msg-s-message-list-content");
  if (!messageList) {
    console.log(`Contact ${contactNumber}: Not in chat interface`);
    return;
  }

  // Find all message event elements
  const messageEvents = messageList.querySelectorAll(".msg-s-message-list__event");

  if (messageEvents.length === 0) {
    console.log(`Contact ${contactNumber}: No message events found`);
    return;
  }

  const conversation: Array<{
    from: string;
    sendTime: string;
    message: string;
  }> = [];
  let lastTimeStamp = "";

  for (let index = 0; index < messageEvents.length; index++) {
    const event = messageEvents[index];

    // Check for date header in this message event
    const dateHeaderElement = event.querySelector(".msg-s-message-list__time-heading");
    if (dateHeaderElement) {
      const dateText = dateHeaderElement.textContent?.trim();
      if (dateText) {
        lastTimeStamp = dateText;
      }
    }

    // Check if this is a sent message (our side - assistant)
    // LinkedIn doesn't have a clear sent/received class distinction, so we'll check the profile link
    const profileLink = event.querySelector(".msg-s-event-listitem__link") as HTMLAnchorElement;
    if (profileLink) {
      const profileUrl = profileLink.href;
      
      // Extract the actual message timestamp
      const timeElement = event.querySelector(".msg-s-message-group__timestamp");
      const messageTime = timeElement ? timeElement.textContent?.trim() || "" : "";
      const fullTimeStamp = lastTimeStamp && messageTime ? `${lastTimeStamp} ${messageTime}` : (messageTime || lastTimeStamp || `message-${index}`);
      
      // Check if this message is from our profile (we need to determine this)
      // For now, we'll assume messages with certain indicators are from us
      const messageBody = event.querySelector(".msg-s-event-listitem__body");
      if (messageBody) {
        const messageContent = messageBody.textContent?.trim() || "";
        
        // Check if this message has a "sent" indicator
        const sentIndicator = event.querySelector('[data-test-msg-cross-pillar-message-sending-indicator-presenter__sending-indicator--sent]');
        
        if (sentIndicator) {
          // This is a sent message (from us - assistant)
          conversation.push({
            from: "assistant",
            sendTime: fullTimeStamp,
            message: messageContent,
          });
        } else {
          // This is a received message (from contact - user)
          conversation.push({
            from: "user",
            sendTime: fullTimeStamp,
            message: messageContent,
          });
        }
      }
    }

    // Check for profile card (initial contact info)
    const profileCard = event.querySelector(".msg-s-profile-card");
    if (profileCard) {
      console.log(`Contact ${contactNumber}: Found profile card at index ${index}`);
      // This is typically the initial profile information, we can skip or log it
    }
  }

  if (conversation.length > 0) {
    console.log(`Contact ${contactNumber} (CID: ${cid}) - Conversation:`);
    console.log(JSON.stringify(conversation, null, 2));

    const cozeConfigData = getCozeConfig();

    if (cozeConfigData && data) {
      // Check if the latest message is from user
      const latestMessage = conversation[conversation.length - 1];
      if (!latestMessage || latestMessage.from !== "user") {
        console.log(`Contact ${contactNumber}: Latest message is not from user, skipping processing`);
        return;
      }

      // Extract latest user messages for userInput field
      const userInput = extractLatestUserMessages(conversation);

      const response = await request.postV2({
        data: {
          functionKey: 'rcn_plugin_ai_get_chat_params',
          params: {
            site: 4,
            jobRequirementId: data.id,
            positionName: data.processName,
            action: 2,
            chatContext: JSON.stringify(conversation),
            userInput: userInput,
          }
        }
      });

      await replyToUser(response.data, contactNumber);
    }
  } else {
    console.log(`Contact ${contactNumber}: No conversation messages found`);
  }
}

async function replyToUser(response: any, contactNumber: number): Promise<void> {
  console.log(`Contact ${contactNumber}: Replying to LinkedIn user...`);

  const cozeConfigData = getCozeConfig();
  
  if (!cozeConfigData) {
    console.log("No coze config available for LinkedIn reply");
    return;
  }

  const cozeResponse = await request.postV2({
    data: {
      functionKey: 'rcn_plugin_ai_execute_workflow',
      params: {
        appId: cozeConfigData.appId,
        workflowId: cozeConfigData.onlineChatFlowId,
        ...response,
      }
    }
  });

  console.log(`Contact ${contactNumber}: Coze API Response:`, cozeResponse);

  // Extract the message from cozeResponse
  if (cozeResponse?.code === 0 && cozeResponse?.data) {
    try {
      // Parse the data field which is a JSON string
      const responseData = JSON.parse(cozeResponse.data);
      const messageToSend = responseData.output;
      
      if (messageToSend) {
        console.log(`Contact ${contactNumber}: Extracted message:`, messageToSend);
        
        // Find the LinkedIn message input box
        const messageInput = document.querySelector('.msg-form__contenteditable[contenteditable="true"]') as HTMLDivElement;
        
        if (messageInput) {
          // Clear existing content and set new message
          messageInput.innerHTML = `<p>${messageToSend}</p>`;
          
          // Focus the input to make it active
          messageInput.focus();
          
          // Trigger input event to notify LinkedIn that content has changed
          const inputEvent = new Event('input', { bubbles: true });
          messageInput.dispatchEvent(inputEvent);
          
          console.log(`Contact ${contactNumber}: Message inserted into LinkedIn chat box`);
          
          await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });
          const sendButton = document.querySelector('.msg-form__send-button') as HTMLButtonElement;
          if (sendButton && !sendButton.disabled) {
            sendButton.click();
            console.log(`Contact ${contactNumber}: Message sent automatically`);
          }
        } else {
          console.log(`Contact ${contactNumber}: LinkedIn message input box not found`);
        }
      } else {
        console.log(`Contact ${contactNumber}: No output message found in response`);
      }
    } catch (parseError) {
      console.log(`Contact ${contactNumber}: Error parsing coze response data:`, parseError);
    }
  } else {
    console.log(`Contact ${contactNumber}: Invalid coze response format`);
  }
}

async function handleContactProfile(
  profileElement: Element,
  conversation: Array<{ from: string; sendTime: string; message: string }>,
  timeStamp: string,
  index: number,
  contactNumber: number
): Promise<void> {
  console.log(`Contact ${contactNumber}: Handling LinkedIn contact profile...`);
  
  // TODO: Implement LinkedIn-specific profile handling
  // This should extract profile information like phone numbers, emails, etc.
  
  console.log(`Contact ${contactNumber}: LinkedIn profile handling not yet implemented`);
}

async function checkAllContacts(
  contactItems: NodeListOf<Element>
): Promise<void> {
  console.log("Checking all LinkedIn contacts...");
  
  // TODO: Implement LinkedIn-specific contact checking
  // This should iterate through all LinkedIn contacts
  
  console.log("LinkedIn contact checking not yet implemented");
}

async function logReceivedMessages(contactNumber: number): Promise<void> {
  console.log(`Contact ${contactNumber}: Logging LinkedIn received messages...`);
  
  // TODO: Implement LinkedIn-specific message logging
  // This should find and log received messages in LinkedIn's interface
  
  console.log(`Contact ${contactNumber}: LinkedIn message logging not yet implemented`);
}

async function handleUnreadMessage(
  messageInfo: UnreadMessageInfo
): Promise<void> {
  console.log(
    `Handling unread LinkedIn message at index ${messageInfo.index} with count: ${messageInfo.count}`
  );

  try {
    // Add human-like delay before clicking
    await addRandomDelay();

    // Click the contact element
    messageInfo.contactElement.click();
    console.log("Successfully clicked LinkedIn contact");

    // Wait for chat window to load
    await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

    // Check if we're in a chat window and process received messages
    await processReceivedMessages();

    // Update statistics
    await updateMessageStats();
  } catch (error) {
    console.error("Error handling LinkedIn message:", error);
    throw error;
  }
}

async function processReceivedMessages(): Promise<void> {
  console.log("Processing received messages in LinkedIn chat window...");

  // TODO: Implement LinkedIn-specific message processing
  // This should find and process received messages in LinkedIn's chat interface
  
  console.log("LinkedIn message processing not yet implemented");
}

async function updateMessageStats(): Promise<void> {
  try {
    const today = new Date().toDateString();
    const statsKey = "linkedin-message-stats";
    const existingStats = localStorage.getItem(statsKey);

    let stats = {
      todayProcessed: 0,
      weekProcessed: 0,
      lastProcessed: today,
    };

    if (existingStats) {
      const parsed = JSON.parse(existingStats);
      stats = { ...stats, ...parsed };

      // Reset daily count if it's a new day
      if (stats.lastProcessed !== today) {
        stats.todayProcessed = 0;
        stats.lastProcessed = today;
      }
    }

    stats.todayProcessed++;
    stats.weekProcessed++;

    localStorage.setItem(statsKey, JSON.stringify(stats));
    console.log("Updated LinkedIn message statistics:", stats);
  } catch (error) {
    console.error("Error updating LinkedIn message stats:", error);
  }
}

export function createLinkedInHandler(configData?: any): MessageHandler {
  // Set the coze configuration if provided
  if (configData) {
    setCozeConfig(configData);
  }

  const config = createLinkedInConfig();

  const handlerFunctions = {
    checkForNewMessages,
    handleUnreadMessage,
    getNextInterval: IntervalPresets.moderate, // Use moderate randomization preset
  };

  return createBaseMessageHandler(config, handlerFunctions);
} 