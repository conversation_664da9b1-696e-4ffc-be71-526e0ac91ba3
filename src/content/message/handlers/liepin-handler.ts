import type {
  Message<PERSON><PERSON><PERSON>,
  UnreadMessageInfo,
} from "../types/message-handler";
import { createBaseMessageHandler } from "./base-message-handler";
import { IntervalPresets, addRandomDelay } from "../utils/randomization";
import request from "@/utils/plugin-request";
import config from "@/config";

// Global variable to store coze configuration
let cozeConfig: any = null;

function createLiepinConfig() {
  return {
    websiteName: "Liepin",
    urlPattern: "liepin.com/im/",
    checkInterval: 30000, // This will be overridden by getNextInterval
  };
}

// Helper function to set coze configuration
function setCozeConfig(configData: any): void {
  cozeConfig = configData;
  console.log("Coze config set in liepin handler:", cozeConfig);
}

// Helper function to get coze configuration
function getCozeConfig(): any {
  return cozeConfig;
}

// Function to extract latest user messages from conversation
function extractLatestUserMessages(
  conversation: Array<{ from: string; sendTime: string; message: string }>
): string {
  // Find the latest user messages (consecutive user messages from the end)
  const userMessages: string[] = [];

  // Start from the end and collect consecutive user messages
  for (let i = conversation.length - 1; i >= 0; i--) {
    const message = conversation[i];
    if (message.from === "user") {
      userMessages.unshift(message.message); // Add to beginning to maintain order
    } else {
      // Stop when we encounter a non-user message
      break;
    }
  }

  return userMessages.join("\n");
}

async function checkForNewMessages(): Promise<UnreadMessageInfo[]> {
  console.log("Checking for new messages on Liepin...");

  // Query all contact list items
  const contactItems = document.querySelectorAll(".__im_pro__list-item");
  console.log(`Found ${contactItems.length} contact items`);

  if (contactItems.length === 0) {
    console.log("No contact items found");
    return [];
  }

  const unreadMessages: UnreadMessageInfo[] = [];
  const unreadMessageElements: Element[] = [];

  // Check each contact item for unread messages
  contactItems.forEach((item, index) => {
    // Look for the unread indicator div with display: block
    const unreadIndicator = item.querySelector(
      'div[style*="display: block"]'
    ) as HTMLElement;

    if (unreadIndicator) {
      const unreadCountText = unreadIndicator.textContent?.trim() || "0";
      const unreadCount = parseInt(unreadCountText, 10) || 0;

      if (unreadCount > 0) {
        console.log(
          `Found unread message indicator at index ${index} with count: ${unreadCount}`
        );

        // Find the clickable contact element
        const contactElement = item.querySelector(
          ".__im_pro__contact-item"
        ) as HTMLElement;

        if (contactElement) {
          // Store the original .__im_pro__list-item element for checkAllContactsForCID
          unreadMessageElements.push(item);
          
          // Store the UnreadMessageInfo for the interface requirement
          unreadMessages.push({
            count: unreadCount,
            contactElement,
            index,
          });
        }
      }
    }
  });

  // If no unread messages found, check each contact individually for testing
  if (unreadMessageElements.length === 0 && contactItems.length > 0) {
    console.log(
      "No unread indicators found, checking each contact individually for CID extraction..."
    );
    await checkAllContactsForCID(contactItems);
  }

  return unreadMessages;
}

async function checkAllContactsForCID(
  contactItems: NodeListOf<Element>
): Promise<void> {
  for (let i = 0; i < contactItems.length; i++) {
    const item = contactItems[i];
    const contactElement = item.querySelector(
      ".__im_pro__contact-item"
    ) as HTMLElement;

    if (!contactElement) continue;

    console.log(`Checking contact ${i + 1}/${contactItems.length} for CID...`);

    try {
      // Add delay before clicking
      await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

      // Click the contact
      contactElement.click();

      // Wait for chat to load
      await addRandomDelay({ minDelay: 1500, maxDelay: 2500 });

      // Extract CID from resume button
      await extractCIDFromResumeButton(i + 1);
    } catch (error) {
      console.error(`Error checking contact ${i + 1}:`, error);
      continue;
    }
  }

  console.log("Finished checking all contacts for CID");
}

async function extractCIDFromResumeButton(
  contactNumber: number
): Promise<void> {
  // Look for the resume button
  const resumeButton = document.querySelector(
    ".__im_pro__action-svg-item.action-item.action-resume"
  ) as HTMLElement;

  if (!resumeButton) {
    console.log(`Contact ${contactNumber}: Resume button not found`);
    return;
  }

  // Get the data-tlg-scm attribute
  const tlgScmData = resumeButton.getAttribute("data-tlg-scm");

  if (!tlgScmData) {
    console.log(`Contact ${contactNumber}: data-tlg-scm attribute not found`);
    return;
  }

  // Extract CID from the data-tlg-scm string
  // Format: "cid=d5ef832071J0413e8b6f403&ctype=2&traceId=..."
  const cidMatch = tlgScmData.match(/cid=([^&]+)/);

  if (cidMatch && cidMatch[1]) {
    const cid = cidMatch[1];
    console.log(`Contact ${contactNumber}: Extracted CID = ${cid}`);

    // TODO 
    const apiUrl = `${config.api}/api/company/1/ai/match/jobRequirement/thirdPartyResume?resumeId=${cid}&site=2`;

    console.log(`Contact ${contactNumber}: Making API request to: ${apiUrl}`);

    // const response = await request.get(apiUrl);
    const response = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_resume_match_project',
        params: {
          resumeId: cid,
          site: 2
        }
      }
    });
    console.log(`Contact ${contactNumber}: API Response:`, response.data);

    // After getting CID, extract the conversation
    await extractConversation(contactNumber, cid, response.data);
  } else {
    console.log(
      `Contact ${contactNumber}: Could not extract CID from data-tlg-scm: ${tlgScmData}`
    );
  }
}

async function extractConversation(
  contactNumber: number,
  cid: string,
  data: any
): Promise<void> {
  // Check if we're in the chat interface
  const chatList = document.querySelector(".__im_pro__chat-list");
  if (!chatList) {
    console.log(`Contact ${contactNumber}: Not in chat interface`);
    return;
  }

  // Find all message wrapper elements (both types)
  const messageWrappers = chatList.querySelectorAll(
    ".__im_pro__message-wrapper, .__im_basic__message-wrapper"
  );

  if (messageWrappers.length === 0) {
    console.log(`Contact ${contactNumber}: No message wrappers found`);
    return;
  }

  const conversation: Array<{
    from: string;
    sendTime: string;
    message: string;
  }> = [];
  let lastTimeStamp = "";

  for (let index = 0; index < messageWrappers.length; index++) {
    const wrapper = messageWrappers[index];

    // Check for time stamp in this message wrapper
    const timeElement = wrapper.querySelector(".__im_UI__system-tip");
    if (timeElement) {
      const timeText = timeElement.textContent?.trim();
      if (timeText && !timeText.includes("沟通职位")) {
        lastTimeStamp = timeText;
      }
    }

    // Check if this is a sent message (our side - assistant)
    const sentMessageBody = wrapper.querySelector(".__im_pro__message-send");
    if (sentMessageBody) {
      const contentElement = sentMessageBody.querySelector(
        ".__im_UI__txt-content"
      ) as HTMLElement;
      if (contentElement) {
        const messageContent = contentElement.textContent?.trim() || "";
        conversation.push({
          from: "assistant",
          sendTime: lastTimeStamp || `message-${index}`,
          message: messageContent,
        });
      }
    }

    // Check if this is a received message (contact side - user)
    const receivedMessageBody = wrapper.querySelector(
      ".__im_pro__message-receive"
    );
    if (receivedMessageBody) {
      // Check if this is a resume message
      const resumeCard = receivedMessageBody.querySelector(
        ".__im_basic__send-resume-card"
      );
      if (resumeCard) {
        conversation.push({
          from: "user",
          sendTime: lastTimeStamp || `message-${index}`,
          message: "我已将简历发送给您",
        });
        console.log(
          `Contact ${contactNumber}: Found resume message at index ${index}`
        );
      } else {
        // Regular text message
        const contentElement = receivedMessageBody.querySelector(
          ".__im_UI__txt-content"
        ) as HTMLElement;
        if (contentElement) {
          const messageContent = contentElement.textContent?.trim() || "";
          conversation.push({
            from: "user",
            sendTime: lastTimeStamp || `message-${index}`,
            message: messageContent,
          });
        }
      }
    }

    // Check for special universal card (phone number sharing)
    const universalCard = wrapper.querySelector(".__im_basic__universal-card");
    if (universalCard) {
      console.log(
        `Contact ${contactNumber}: Found universal card in wrapper ${index}`
      );
      await handleUniversalCard(
        universalCard,
        conversation,
        lastTimeStamp,
        index,
        contactNumber
      );
    }
  }

  if (conversation.length > 0) {
    console.log(`Contact ${contactNumber} (CID: ${cid}) - Conversation:`);
    console.log(JSON.stringify(conversation, null, 2));

    const cozeConfigData = getCozeConfig();

    if (cozeConfigData && data) {
      // Check if the latest message is from user
      const latestMessage = conversation[conversation.length - 1];
      if (!latestMessage || latestMessage.from !== "user") {
        console.log(`Contact ${contactNumber}: Latest message is not from user, skipping processing`);
        return;
      }

      // Extract latest user messages for userInput field
      const userInput = extractLatestUserMessages(conversation);

      const response = await request.postV2({
        data: {
          functionKey: 'rcn_plugin_ai_get_chat_params',
          params: {
            site: 2,
            jobRequirementId: data.id,
            positionName: data.processName,
            action: 2,
            chatContext: JSON.stringify(conversation),
            userInput: userInput,
          }
        }
      });

      await replyToUser(response.data, contactNumber);
    }
  } else {
    console.log(`Contact ${contactNumber}: No conversation messages found`);
  }
}

// now add a new function to handle the coze api response, name it reply to user, and it will reply to the user with the coze api response
async function replyToUser(response: any, contactNumber: number): Promise<void> {
  console.log(`Contact ${contactNumber}: Replying to Liepin user...`);

  const cozeConfigData = getCozeConfig();
  
  if (!cozeConfigData) {
    console.log("No coze config available for Liepin reply");
    return;
  }

  try {
    const cozeResponse = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_ai_execute_workflow',
        params: {
          workflowData: {
            params: {
              ...response,
            },
          },
          appId: cozeConfigData.appId,
          workflowId: cozeConfigData.onlineChatFlowId
        }
      }
    });
    console.log(`res: `, cozeResponse);

  console.log(`Contact ${contactNumber}: Coze API Response:`, cozeResponse);

  // Extract the message from cozeResponse
  if (cozeResponse?.code === 0 && cozeResponse?.data) {
    try {
      // Parse the data field which is a JSON string
      const responseData = JSON.parse(cozeResponse.data);
      const messageToSend = responseData.output;
      
      if (messageToSend) {
        console.log(`Contact ${contactNumber}: Extracted message:`, messageToSend);
        
        // Find the Liepin message input box
        const messageInput = document.querySelector('.__im_pro__textarea') as HTMLTextAreaElement;
        
        if (messageInput) {
          // Clear existing content and set new message
          messageInput.value = messageToSend;
          
          // Focus the input to make it active
          messageInput.focus();
          
          // Trigger input event to notify Liepin that content has changed
          const inputEvent = new Event('input', { bubbles: true });
          messageInput.dispatchEvent(inputEvent);
          
          console.log(`Contact ${contactNumber}: Message inserted into Liepin chat box`);
          
          await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });
          const sendButton = document.querySelector('.__im_pro__btn-send') as HTMLButtonElement;
          if (sendButton && !sendButton.disabled) {
            sendButton.click();
            console.log(`Contact ${contactNumber}: Message sent automatically`);
          }
        } else {
          console.log(`Contact ${contactNumber}: Liepin message input box not found`);
        }
      } else {
        console.log(`Contact ${contactNumber}: No output message found in response`);
      }
    } catch (parseError) {
      console.log(`Contact ${contactNumber}: Error parsing coze response data:`, parseError);
    }
  } else {
    console.log(`Contact ${contactNumber}: Invalid coze response format`);
  }
  } catch (e) {
    console.log(`e: `, e)
  }
}

async function handleUniversalCard(
  universalCard: Element,
  conversation: Array<{ from: string; sendTime: string; message: string }>,
  timeStamp: string,
  index: number,
  contactNumber: number
): Promise<void> {
  // First check if this is a WeChat exchange request card (with 同意/拒绝 buttons)
  const exchangeCardContent = universalCard.querySelector(".__im_basic__universal-card-content");
  if (exchangeCardContent) {
    const contentText = exchangeCardContent.textContent?.trim() || "";
    
    // Check if this is a WeChat exchange request
    if (contentText.includes("交换微信号") || contentText.includes("可以交换微信号吗")) {
      console.log(`Contact ${contactNumber}: Found WeChat exchange request card: ${contentText}`);
      
      // Look for the "同意" (Agree) button
      const agreeButton = universalCard.querySelector('.__im_basic__universal-card-btn[btntext="同意"]') as HTMLElement;
      
      if (agreeButton) {
        console.log(`Contact ${contactNumber}: Found agree button, clicking...`);
        
        try {
          // Add small delay before clicking
          await addRandomDelay({ minDelay: 500, maxDelay: 1000 });
          
          // Click the agree button
          agreeButton.click();
          console.log(`Contact ${contactNumber}: Successfully clicked agree button for WeChat exchange`);
          
          // Wait for the action to process
          await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });
          
          // Add to conversation as a user message
          conversation.push({
            from: "user",
            sendTime: timeStamp || `message-${index}`,
            message: "同意交换微信号",
          });
          
          return; // Exit early as we've handled this card
        } catch (error) {
          console.error(`Contact ${contactNumber}: Error clicking agree button:`, error);
        }
      } else {
        console.log(`Contact ${contactNumber}: Agree button not found in WeChat exchange card`);
      }
      return; // Exit as this was a WeChat exchange request card
    }
  }

  // Check if this is a phone number or WeChat ID sharing card
  const cardHeader = universalCard.querySelector(
    ".__im_basic__universal-card-header"
  );
  if (!cardHeader) {
    return;
  }

  const headerText = cardHeader.textContent?.trim() || "";
  const isPhoneCard = headerText.includes("手机号");
  const isWeChatCard = headerText.includes("微信号");

  if (!isPhoneCard && !isWeChatCard) {
    return;
  }

  // Get the card content
  const cardContent = universalCard.querySelector(
    ".__im_basic__universal-card-content"
  );
  if (!cardContent) {
    return;
  }

  let contentText = cardContent.textContent?.trim() || "";
  console.log(
    `Contact ${contactNumber}: Found ${
      isPhoneCard ? "phone" : "WeChat"
    } card: ${contentText}`
  );

  if (isPhoneCard) {
    // Handle phone number card
    // Check if phone number has stars (hidden)
    if (contentText.includes("*")) {
      console.log(
        `Contact ${contactNumber}: Phone number is hidden, attempting to reveal...`
      );

      // Find and click the view button
      const viewButton = universalCard.querySelector(
        ".__im_basic__universal-card-btn"
      ) as HTMLElement;
      if (viewButton && viewButton.textContent?.includes("查看")) {
        try {
          // Add small delay before clicking
          await addRandomDelay({ minDelay: 500, maxDelay: 1000 });

          viewButton.click();
          console.log(
            `Contact ${contactNumber}: Clicked view button for phone number`
          );

          // Wait for the phone number to be revealed
          await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

          // Get the updated phone number
          const updatedContent = universalCard.querySelector(
            ".__im_basic__universal-card-content"
          );
          if (updatedContent) {
            contentText = updatedContent.textContent?.trim() || contentText;
            console.log(
              `Contact ${contactNumber}: Updated phone text: ${contentText}`
            );
          }
        } catch (error) {
          console.error(
            `Contact ${contactNumber}: Error clicking view button:`,
            error
          );
        }
      }
    }

    // Extract the actual phone number from the text
    // Format: "舒先生发送的手机号：139****9952" or "舒先生发送的手机号：13912345678"
    const phoneMatch = contentText.match(/手机号：(.+)$/);
    const phoneNumber = phoneMatch ? phoneMatch[1] : contentText;

    // Add to conversation as a user message
    conversation.push({
      from: "user",
      sendTime: timeStamp || `message-${index}`,
      message: `这是我的手机号：${phoneNumber}`,
    });

    console.log(
      `Contact ${contactNumber}: Added phone number to conversation: ${phoneNumber}`
    );
  }

  if (isWeChatCard) {
    // Handle WeChat ID card
    // Check if WeChat ID has stars (hidden)
    if (contentText.includes("*")) {
      console.log(
        `Contact ${contactNumber}: WeChat ID is hidden, attempting to reveal...`
      );

      // Find and click the view button
      const viewButton = universalCard.querySelector(
        ".__im_basic__universal-card-btn"
      ) as HTMLElement;
      if (viewButton && viewButton.textContent?.includes("查看")) {
        try {
          // Add small delay before clicking
          await addRandomDelay({ minDelay: 500, maxDelay: 1000 });

          viewButton.click();
          console.log(
            `Contact ${contactNumber}: Clicked view button for WeChat ID`
          );

          // Wait for the WeChat ID to be revealed
          await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

          // Get the updated WeChat ID
          const updatedContent = universalCard.querySelector(
            ".__im_basic__universal-card-content"
          );
          if (updatedContent) {
            contentText = updatedContent.textContent?.trim() || contentText;
            console.log(
              `Contact ${contactNumber}: Updated WeChat text: ${contentText}`
            );
          }
        } catch (error) {
          console.error(
            `Contact ${contactNumber}: Error clicking view button:`,
            error
          );
        }
      }
    }

    // Extract the WeChat ID from the text
    // Format: "顾女士发送的微信号：vi****kl" or "顾女士发送的微信号：vincyskl"
    const wechatMatch =
      contentText.match(/.*?发送的微信号：(.+)$/) ||
      contentText.match(/微信号：(.+)$/);
    const wechatId = wechatMatch ? wechatMatch[1] : contentText;

    // Add to conversation as a user message
    conversation.push({
      from: "user",
      sendTime: timeStamp || `message-${index}`,
      message: `这是我的微信号：${wechatId}`,
    });

    console.log(
      `Contact ${contactNumber}: Added WeChat ID to conversation: ${wechatId}`
    );
  }
}

async function checkAllContacts(
  contactItems: NodeListOf<Element>
): Promise<void> {
  for (let i = 0; i < contactItems.length; i++) {
    const item = contactItems[i];
    const contactElement = item.querySelector(
      ".__im_pro__contact-item"
    ) as HTMLElement;

    if (!contactElement) continue;

    console.log(`Checking contact ${i + 1}/${contactItems.length}...`);

    try {
      // Add delay before clicking
      await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

      // Click the contact
      contactElement.click();

      // Wait for chat to load
      await addRandomDelay({ minDelay: 1500, maxDelay: 2500 });

      // Check for received messages and log them
      await logReceivedMessages(i + 1);
    } catch (error) {
      console.error(`Error checking contact ${i + 1}:`, error);
      continue;
    }
  }

  console.log("Finished checking all contacts");
}

async function logReceivedMessages(contactNumber: number): Promise<void> {
  // Check if we're in the chat interface
  const chatList = document.querySelector(".__im_pro__chat-list");
  if (!chatList) {
    console.log(`Contact ${contactNumber}: Not in chat interface`);
    return;
  }

  // Look for received messages
  const receivedMessages = chatList.querySelectorAll(".__im_UI__txt.receive");

  if (receivedMessages.length === 0) {
    console.log(`Contact ${contactNumber}: No received messages`);
    return;
  }

  console.log(
    `Contact ${contactNumber}: Found ${receivedMessages.length} received messages:`
  );

  // Log each received message
  receivedMessages.forEach((messageElement, index) => {
    const contentElement = messageElement.querySelector(
      ".__im_UI__txt-content"
    ) as HTMLElement;
    if (contentElement) {
      const messageContent = contentElement.textContent?.trim() || "";
      console.log(
        `Contact ${contactNumber} - Message ${index + 1}: ${messageContent}`
      );
    }
  });
}

async function handleUnreadMessage(
  messageInfo: UnreadMessageInfo
): Promise<void> {
  console.log(
    `Handling unread message at index ${messageInfo.index} with count: ${messageInfo.count}`
  );

  try {
    // Add human-like delay before clicking
    await addRandomDelay();

    // Click the contact element
    messageInfo.contactElement.click();
    console.log("Successfully clicked contact");

    // Wait for chat window to load
    await addRandomDelay({ minDelay: 1000, maxDelay: 2000 });

    // Check if we're in a chat window and process received messages
    await processReceivedMessages();

    // Update statistics
    await updateMessageStats();
  } catch (error) {
    console.error("Error handling message:", error);
    throw error;
  }
}

async function processReceivedMessages(): Promise<void> {
  console.log("Processing received messages in chat window...");

  // Check if we're in the chat interface
  const chatList = document.querySelector(".__im_pro__chat-list");
  if (!chatList) {
    console.log("Not in chat interface, skipping message processing");
    return;
  }

  console.log("Found chat interface, looking for received messages...");

  // Find all received messages
  const receivedMessages = chatList.querySelectorAll(".__im_UI__txt.receive");
  console.log(`Found ${receivedMessages.length} received messages`);

  if (receivedMessages.length === 0) {
    console.log("No received messages found in chat");
    return;
  }

  // Process each received message
  receivedMessages.forEach((messageElement, index) => {
    const contentElement = messageElement.querySelector(
      ".__im_UI__txt-content"
    ) as HTMLElement;
    if (contentElement) {
      const messageContent = contentElement.textContent?.trim() || "";
      console.log(
        `Received message ${index + 1}:`,
        messageContent.substring(0, 100) +
          (messageContent.length > 100 ? "..." : "")
      );

      // Here you can add logic to process the message content
      // For example: keyword detection, automated responses, etc.
      // processMessageContent(messageContent, index);
    }
  });
}

async function updateMessageStats(): Promise<void> {
  try {
    const today = new Date().toDateString();
    const statsKey = "liepin-message-stats";
    const existingStats = localStorage.getItem(statsKey);

    let stats = {
      todayProcessed: 0,
      weekProcessed: 0,
      lastProcessed: today,
    };

    if (existingStats) {
      const parsed = JSON.parse(existingStats);
      stats = { ...stats, ...parsed };

      // Reset daily count if it's a new day
      if (stats.lastProcessed !== today) {
        stats.todayProcessed = 0;
        stats.lastProcessed = today;
      }
    }

    stats.todayProcessed++;
    stats.weekProcessed++;

    localStorage.setItem(statsKey, JSON.stringify(stats));
    console.log("Updated message statistics:", stats);
  } catch (error) {
    console.error("Error updating message stats:", error);
  }
}

export function createLiepinHandler(configData?: any): MessageHandler {
  // Set the coze configuration if provided
  if (configData) {
    setCozeConfig(configData);
  }

  const config = createLiepinConfig();

  const handlerFunctions = {
    checkForNewMessages,
    handleUnreadMessage,
    getNextInterval: IntervalPresets.moderate, // Use moderate randomization preset
  };

  return createBaseMessageHandler(config, handlerFunctions);
}
