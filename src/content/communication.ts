import manifest from "@/manifest.json"
import { initDebugCommands } from '@/utils/debug-console-commands'

/**
 * 生成唯一字符串
*/
const unique = (() => {
	let number = Date.now()
	return () => {
		number++
		return 'unique_' + number
	}
})()

/**
 * 发起插件消息通讯
 * @param { String } callName - 通话名称
 * @param { Object } data - 发送的数据
 * @param { Number } timeout - 超时时间 默认30s无应答视为超时
*/
function callToBackground(callName: string, data?: any, timeout = 1000 * 30) {
	if (!callName || typeof callName !== 'string') {
		throw new Error('无效的通话名称')
	}
	const nextCallName = 'browse_plugin:' + callName
	return new Promise((resolve, reject) => {
		let _timer: any = null
		let _doTry = false
		chrome.runtime.sendMessage({
			callName: nextCallName,
			data
		}, (evt) => {
			clearTimeout(_timer)
			if (_doTry) {
				return
			}
			if (evt) {
				if (evt.status === 'error') {
					reject(evt.data)
				} else {
					resolve(evt.data)
				}
			} else {
				resolve(evt)
			}
		})
		_timer = setTimeout(() => {
			_doTry = true
			reject('timeout')
		}, timeout)
	})
}


const Apis: { [key: string]: any } = {
	/**
	 * 插件是否激活
	 * 后续可以返回插件版本信息、支持的招聘网站信息
	*/
	checkPluginActive() {
		return new Promise((resolve, reject) => {
			callToBackground('checkPluginActive').then(() => {
				resolve({
					name: manifest.name,
					version: manifest.version,
					id: chrome.runtime.id
				})
			}, (err) => {
				reject(err)
			})
		})
	},
	/**
	 * 发送异步请求
	*/
	request(options: any) {
		return new Promise((resolve, reject) => {
			callToBackground('request', options).then((rs) => {
				resolve(rs)
			}, reject)
		})
	},

	setLocalCache(data: any) {
		return new Promise((resolve, reject) => {
			callToBackground('setLocalCache', data).then((rs) => {
				resolve(rs)
			}, reject)
		})
	},

	/**
	 * 清除认证令牌
	 */
	clearAuthTokens() {
		return new Promise((resolve, reject) => {
			callToBackground('clearAuthTokens').then((rs) => {
				resolve(rs)
			}, reject)
		})
	}
}

/**
 * 向客户端消费回拨
 * @param { String } callName - 呼叫名称
 * @param { String } status - 状态，success：成功；error：失败
 * @param { Object } data - 返回的数据，如果是promise，那么会处理promise状态
*/
function callToClient(callName: string, data: any) {
	const createEvent = (status: any, data: any) => {
		return new CustomEvent('browse_plugin_recruit_consume', {
			detail: {
				callName,
				data: {
					status,
					result: data
				}
			}
		})
	}
	if (data instanceof Promise) {
		data.then((rs) => {
			window.document.dispatchEvent(createEvent('success', rs))
		}, (err) => {
			window.document.dispatchEvent(createEvent('error', err))
		})
	} else {
		window.document.dispatchEvent(createEvent('success', data))
	}
}

/**
 * 监听页面事件
*/
window.document.addEventListener('browse_plugin_recruit', (evt: any) => {
	const { callName, data } = evt.detail
	const methodName: string = callName.substr(callName.indexOf(':') + 1)
	if (!methodName) {
		callToClient(callName, Promise.reject('[BrowsePlugin] 无效的callName'))
	} else if (!Apis[methodName]) {
		callToClient(callName, Promise.reject(`[BrowsePlugin] 不支持的${methodName}`))
	} else {
		callToClient(callName, Apis[methodName](data))
	}
})

// 初始化调试命令
initDebugCommands();