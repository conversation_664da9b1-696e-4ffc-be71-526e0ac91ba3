<template lang="pug">
.dupliate-talent-list
  .talent-item(v-for="(talent, index) in talents" :key="talent.id")
    TalentInfo(:talent="talents[index]" @click="() => gotoTalentDetail(talent)")
    TalentExp(:talent="talentDetails[index]")
    .duplicate-talent-actions
      Row(:gutter="[12, 12]")
        Col(:span="12")
          But<PERSON>(type="primary" block @click="() => handleUseTalentClick(talent)") 使用人才信息
        Col(:span="12")
          <PERSON><PERSON>(block @click="() => handleNoDuplicateFeedback(talent)") 不是重复人才
    Divider(v-if="index != talents.length - 1")
    
</template>

<script lang="ts" setup>
import { onMounted, ref, toRef } from 'vue'
import TalentInfo from '@/components/talent-info.vue'
import TalentExp from './talent-exp.vue'
import config from '@/config'
import request from '@/utils/plugin-request'
import { message, Divider, But<PERSON>, <PERSON>, <PERSON> } from 'ant-design-vue'
import { EventTracker, useTracker } from '@/utils/track'

const props = defineProps<{ talents: any[], talentId:number, candidateId:number }>()
const talents = toRef(props, 'talents')
const talentId = toRef(props, 'talentId')
const candidateId = toRef(props, 'candidateId')
const talentDetails = ref<any[]>([])
const emit = defineEmits(['select', 'close'])

const track_id = Math.random().toString(36)
const tracker = ref<EventTracker>()


// if (duplicateTalents.value.length) {
//       const talentIds = duplicateTalents.value.map(item => item.id)
//       const track_id = Math.random().toString(36)
//       // 这里打点需要确认talent Ids
//       tracker.value?.event('repeat_talent', {type: 1, track_id, talent_ids: talentIds})
//     }

function handleUseTalentClick(talent: any) {
  console.log('use talent', talent)
  emit('select', talent)
}

function handleNoDuplicateFeedback(talent:any) {
  tracker.value?.click('no_duplicate_feedback', {
     talent_id: talentId.value,
     candidate_id: candidateId.value,
     duplicate_talent_id: talent.id
  })
  emit('close')
}

function gotoTalentDetail(talent: any) {
  tracker.value?.click('view_repeat_talent', { track_id, talent_id: talent.id })
  window.open(`${config.itp}/talent/${talent.id}/detail`)
}

async function fetchTalentDetail() {
  try {
    const requests = talents.value.map(talent => {
      return request.postV2({
        data: {
          functionKey: 'rcn_plugin_talent_detail',
          params: {
            talentId: talent.id || "1111"
          }
        }
      })
    })
    const response = await Promise.all(requests)
    response.forEach((res, index) => {
      console.log(`res: `, res);
      talentDetails.value[index] = res.data || res
    })
  } catch (e: any) {
    console.log(`e: `, e)
    message.error(e.message || '获取人才详情失败')
  }
}

onMounted(async () => {
  console.log(talents.value)
  fetchTalentDetail()

  tracker.value = await useTracker()

  if (talents.value.length) {
    const talentIds = talents.value.map(item => item.id)
    const track_id = Math.random().toString(36)
    tracker.value?.event('show_repeat_talent', { track_id, talent_ids: talentIds })
  }
})

</script>

<style lang="sass" scoped>
.duplicate-talent-actions
  padding: 12px 20px
</style>