<template lang="pug">

mixin job-item
  Row.job-item(:gutter="[12, 12]" @click="() => gotoJobDetail(item.jobRequirement.id)")
    Col(:span="12")
      .job-name
        JobPriority(:priority="item.jobRequirement.priority")
        span {{ item.jobRequirement.processName  }}
      .job-customer
        span {{ item.jobRequirement.customerName }}

    Col(:span="12")
      .task-status
        span {{ item.task.name }}
      .task-time
        span {{ formatDate(item.task.createTime) }}

.talent-job
  Spin(:spinning="status.loading")
    .talent-job-list(v-if="jobList.length")
      template(v-for="(item, index) in jobList")
        +job-item

    .empty(v-else)
      Empty
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef } from 'vue'
import { Empty, Row, Col, Spin, message } from 'ant-design-vue'
import request from '@/utils/plugin-request'
import config from '@/config'
import JobPriority from '@/components/job-priority.vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/content/user-store'

const props = defineProps<{ talentId: number }>()
const talentId = toRef(props, 'talentId')
const jobList = ref<any[]>([])
const emit = defineEmits(['count'])
const userStore = useUserStore()


const status = reactive({
  loading: false
})

async function getTalentJobList(talentId: number) {
  status.loading = true
  try {
    const res = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_get_talent_pipeline',
        params: {
          talentId: talentId
        }
      }
    })
    console.log(`res: `, res);
    jobList.value = res.data || res || []
    emit('count', jobList.value.length)
  } catch (e: any) {
    console.log(`e: `, e)
    message.error(e.message || '获取人才管道失败')
  }
  status.loading = false
}

function formatDate(timestamp: string) {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

function gotoJobDetail(jobId: number) {
  window.open(`${config.itp}/job/${jobId}/detail`)
}

onMounted(() => {
  getTalentJobList(talentId.value)
})

</script>

<style lang="sass" scoped>

.job-item
  padding: 12px 0
  border-bottom: 1px solid #f0f0f0
  line-height: 24px
  cursor: pointer
  transition: all 0.3s

  &:hover
    background-color: #fafafa
    transition: all 0.3s
  .job-customer
    color: #999

  .task-time
    color: #999

</style>