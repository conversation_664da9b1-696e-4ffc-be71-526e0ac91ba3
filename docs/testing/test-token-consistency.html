<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token一致性检测测试 - TypeScript插件</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .result.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .status-item {
            padding: 10px;
            border-radius: 5px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        .status-item.good {
            border-left-color: #27ae60;
            background-color: #d5f4e6;
        }
        .status-item.warning {
            border-left-color: #f39c12;
            background-color: #fef9e7;
        }
        .status-item.error {
            border-left-color: #e74c3c;
            background-color: #fadbd8;
        }
        .token-display {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
            word-break: break-all;
        }
        .scenario-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <h1>🔐 Token一致性检测测试</h1>
    
    <div class="container">
        <h2>📊 当前状态检查</h2>
        <p>检查当前的token缓存状态和一致性检查配置</p>
        <button onclick="checkCurrentStatus()">检查当前状态</button>
        <div id="status-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🔍 一致性检测测试</h2>
        <p>测试localStorage与插件缓存之间的token一致性检测</p>
        
        <div class="scenario-buttons">
            <button onclick="testConsistentTokens()">测试一致Token</button>
            <button onclick="testInconsistentTokens()">测试不一致Token</button>
            <button onclick="testMissingLocalStorage()">测试缺失localStorage</button>
            <button onclick="testMissingCache()">测试缺失缓存</button>
        </div>
        
        <div id="consistency-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🔄 同步功能测试</h2>
        <p>测试token同步和更新功能</p>
        
        <div class="scenario-buttons">
            <button onclick="simulateTokenUpdate()">模拟Token更新</button>
            <button onclick="forceConsistencyCheck()">强制一致性检查</button>
            <button onclick="testAutoSync()">测试自动同步</button>
            <button onclick="clearAllCaches()">清除所有缓存</button>
        </div>
        
        <div id="sync-result" class="result"></div>
    </div>

    <div class="container">
        <h2>⚡ 性能测试</h2>
        <p>测试一致性检查的性能影响和智能检测间隔</p>
        
        <div class="scenario-buttons">
            <button onclick="testPerformanceImpact()">性能影响测试</button>
            <button onclick="testCheckInterval()">检查间隔测试</button>
            <button onclick="testBatchOperations()">批量操作测试</button>
        </div>
        
        <div id="performance-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🛠️ 调试工具</h2>
        <p>用于调试和分析token一致性检测的工具</p>
        
        <div class="scenario-buttons">
            <button onclick="inspectTokens()">检查Token详情</button>
            <button onclick="simulateUserLogin()">模拟用户登录</button>
            <button onclick="exportDebugInfo()">导出调试信息</button>
            <button onclick="resetToCleanState()">重置到干净状态</button>
        </div>
        
        <div id="debug-result" class="result"></div>
    </div>

    <script>
        // 模拟 Base64 编码解码器
        const Base64EncoderDecoder = {
            encrypt: function(input) {
                if (!input) return input;
                const encoded = btoa(input);
                return this.transform(encoded);
            },
            decrypt: function(input) {
                const transformed = this.transform(input);
                const cleaned = transformed.replace(/\*/g, '');
                return atob(cleaned);
            },
            transform: function(input) {
                const blockSize = 8;
                const symbol = '*';
                let inputList = [];
                let tmp = [];

                for (let i = 0; i < input.length; i++) {
                    tmp.push(input.charAt(i));
                    if (tmp.length >= blockSize) {
                        inputList.push(tmp.join(''));
                        tmp = [];
                    }
                }

                if (tmp.length > 0) {
                    inputList.push(tmp.join(''));
                }

                let str = '';
                for (let i = inputList.length - 1; i >= 0; i--) {
                    let t = inputList[i];
                    if (t.length < blockSize) {
                        let len = blockSize - t.length;
                        for (let j = 0; j < len; j++) {
                            t += symbol;
                        }
                    }
                    str = str + t;
                }

                return str;
            }
        };

        // 模拟Chrome存储API
        const mockChromeStorage = {
            local: {
                data: {},
                get: function(keys, callback) {
                    const result = {};
                    if (Array.isArray(keys)) {
                        keys.forEach(key => {
                            if (this.data[key] !== undefined) {
                                result[key] = this.data[key];
                            }
                        });
                    } else if (typeof keys === 'string') {
                        if (this.data[keys] !== undefined) {
                            result[keys] = this.data[keys];
                        }
                    }
                    setTimeout(() => callback(result), 10);
                },
                set: function(items, callback) {
                    Object.assign(this.data, items);
                    setTimeout(() => callback && callback(), 10);
                },
                remove: function(keys, callback) {
                    if (Array.isArray(keys)) {
                        keys.forEach(key => delete this.data[key]);
                    } else {
                        delete this.data[keys];
                    }
                    setTimeout(() => callback && callback(), 10);
                },
                clear: function(callback) {
                    this.data = {};
                    setTimeout(() => callback && callback(), 10);
                }
            }
        };

        // 如果在非Chrome扩展环境中，使用模拟的chrome对象
        if (typeof chrome === 'undefined') {
            window.chrome = mockChromeStorage;
        }

        // 测试用的token生成器
        function generateTestToken(prefix = 'test_token') {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2);
            return `${prefix}_${timestamp}_${random}`;
        }

        // 设置localStorage中的加密token
        function setEncryptedTokenInLocalStorage(token) {
            const encryptedKey = Base64EncoderDecoder.encrypt('auth_token');
            const tokenData = JSON.stringify({ value: token });
            const encryptedValue = Base64EncoderDecoder.encrypt(tokenData);
            localStorage.setItem(encryptedKey, encryptedValue);
            return { encryptedKey, encryptedValue };
        }

        // 获取localStorage中的解密token
        function getDecryptedTokenFromLocalStorage() {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (!key) continue;

                try {
                    const decryptedKey = Base64EncoderDecoder.decrypt(key);
                    if (decryptedKey === 'auth_token') {
                        const encryptedValue = localStorage.getItem(key);
                        const decryptedValue = Base64EncoderDecoder.decrypt(encryptedValue);
                        const tokenData = JSON.parse(decryptedValue);
                        return tokenData.value || tokenData.token || decryptedValue;
                    }
                } catch (error) {
                    continue;
                }
            }
            return null;
        }

        // 日志输出函数
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 检查当前状态
        async function checkCurrentStatus() {
            try {
                logResult('status-result', '正在检查当前状态...', 'info');
                
                let statusInfo = '=== 当前状态检查 ===\n\n';
                
                // 检查localStorage中的token
                const localToken = getDecryptedTokenFromLocalStorage();
                statusInfo += `📱 localStorage Token: ${localToken ? '存在' : '不存在'}\n`;
                if (localToken) {
                    statusInfo += `   Token预览: ${localToken.substring(0, 30)}...\n`;
                }
                
                // 检查Chrome存储中的缓存
                const cacheKeys = [
                    'cached_auth_token',
                    'token_timestamp', 
                    'token_expiry',
                    'last_token_check',
                    'last_consistency_check',
                    'token_hash'
                ];
                
                chrome.storage.local.get(cacheKeys, (cache) => {
                    statusInfo += `\n💾 插件缓存状态:\n`;
                    statusInfo += `   cached_auth_token: ${cache.cached_auth_token ? '存在' : '不存在'}\n`;
                    if (cache.cached_auth_token) {
                        statusInfo += `   Token预览: ${cache.cached_auth_token.substring(0, 30)}...\n`;
                    }
                    statusInfo += `   token_timestamp: ${cache.token_timestamp ? new Date(cache.token_timestamp).toLocaleString() : '无'}\n`;
                    statusInfo += `   token_expiry: ${cache.token_expiry ? new Date(cache.token_expiry).toLocaleString() : '无'}\n`;
                    statusInfo += `   last_consistency_check: ${cache.last_consistency_check ? new Date(cache.last_consistency_check).toLocaleString() : '无'}\n`;
                    statusInfo += `   token_hash: ${cache.token_hash || '无'}\n`;
                    
                    // 检查一致性
                    const isConsistent = localToken && cache.cached_auth_token && localToken === cache.cached_auth_token;
                    statusInfo += `\n🔍 一致性状态: ${isConsistent ? '✅ 一致' : '❌ 不一致或缺失'}\n`;
                    
                    // 检查是否需要一致性检查
                    const now = Date.now();
                    const needsCheck = !cache.last_consistency_check || 
                                     (now - cache.last_consistency_check) >= (5 * 60 * 1000);
                    statusInfo += `⏰ 需要一致性检查: ${needsCheck ? '是' : '否'}\n`;
                    
                    logResult('status-result', statusInfo, isConsistent ? 'success' : 'warning');
                });
                
            } catch (error) {
                logResult('status-result', `状态检查失败: ${error.message}`, 'error');
            }
        }

        // 测试一致的tokens
        async function testConsistentTokens() {
            try {
                logResult('consistency-result', '正在测试一致Token场景...', 'info');
                
                // 生成测试token
                const testToken = generateTestToken('consistent');
                
                // 设置到localStorage
                setEncryptedTokenInLocalStorage(testToken);
                
                // 设置到Chrome存储
                const now = Date.now();
                const cacheData = {
                    cached_auth_token: testToken,
                    token_timestamp: now,
                    token_expiry: now + (24 * 60 * 60 * 1000),
                    last_token_check: now,
                    token_hash: generateSimpleHash(testToken)
                };
                
                chrome.storage.local.set(cacheData, () => {
                    let result = '=== 一致Token测试 ===\n\n';
                    result += `✅ 已设置相同token到localStorage和插件缓存\n`;
                    result += `🔑 测试Token: ${testToken}\n`;
                    result += `📊 预期结果: 一致性检查应该通过，无需同步\n`;
                    result += `\n下一步: 点击"强制一致性检查"验证结果`;
                    
                    logResult('consistency-result', result, 'success');
                });
                
            } catch (error) {
                logResult('consistency-result', `一致Token测试失败: ${error.message}`, 'error');
            }
        }

        // 生成简单hash
        function generateSimpleHash(token) {
            let hash = 0;
            for (let i = 0; i < token.length; i++) {
                const char = token.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString(36);
        }

        // 测试不一致的tokens
        async function testInconsistentTokens() {
            try {
                logResult('consistency-result', '正在测试不一致Token场景...', 'info');
                
                // 生成两个不同的测试token
                const localToken = generateTestToken('local');
                const cacheToken = generateTestToken('cache');
                
                // 设置到localStorage
                setEncryptedTokenInLocalStorage(localToken);
                
                // 设置不同的token到Chrome存储
                const now = Date.now();
                const cacheData = {
                    cached_auth_token: cacheToken,
                    token_timestamp: now,
                    token_expiry: now + (24 * 60 * 60 * 1000),
                    last_token_check: now,
                    token_hash: generateSimpleHash(cacheToken)
                };
                
                chrome.storage.local.set(cacheData, () => {
                    let result = '=== 不一致Token测试 ===\n\n';
                    result += `⚠️ 已设置不同token到localStorage和插件缓存\n`;
                    result += `📱 localStorage Token: ${localToken}\n`;
                    result += `💾 缓存Token: ${cacheToken}\n`;
                    result += `📊 预期结果: 检测到不一致，应该同步localStorage的token到缓存\n`;
                    result += `\n下一步: 点击"强制一致性检查"验证同步功能`;
                    
                    logResult('consistency-result', result, 'warning');
                });
                
            } catch (error) {
                logResult('consistency-result', `不一致Token测试失败: ${error.message}`, 'error');
            }
        }

        // 强制一致性检查
        async function forceConsistencyCheck() {
            try {
                logResult('sync-result', '正在执行强制一致性检查...', 'info');
                
                // 模拟一致性检查逻辑
                const localToken = getDecryptedTokenFromLocalStorage();
                
                chrome.storage.local.get(['cached_auth_token', 'token_hash'], (cache) => {
                    let result = '=== 强制一致性检查结果 ===\n\n';
                    
                    if (!localToken) {
                        result += '❌ localStorage中未找到token\n';
                        result += '📊 结果: 跳过一致性检查';
                        logResult('sync-result', result, 'warning');
                        return;
                    }
                    
                    const isConsistent = localToken === cache.cached_auth_token;
                    
                    result += `📱 localStorage Token: ${localToken ? localToken.substring(0, 30) + '...' : '无'}\n`;
                    result += `💾 缓存Token: ${cache.cached_auth_token ? cache.cached_auth_token.substring(0, 30) + '...' : '无'}\n`;
                    result += `🔍 一致性检查: ${isConsistent ? '✅ 通过' : '❌ 不一致'}\n`;
                    
                    if (!isConsistent) {
                        result += `\n🔄 执行同步操作...\n`;
                        
                        // 模拟同步操作
                        const now = Date.now();
                        const syncData = {
                            cached_auth_token: localToken,
                            token_timestamp: now,
                            token_expiry: now + (24 * 60 * 60 * 1000),
                            last_token_check: now,
                            last_consistency_check: now,
                            token_hash: generateSimpleHash(localToken)
                        };
                        
                        chrome.storage.local.set(syncData, () => {
                            result += `✅ 同步完成\n`;
                            result += `📊 新缓存Token: ${localToken.substring(0, 30)}...\n`;
                            result += `⏰ 同步时间: ${new Date(now).toLocaleString()}`;
                            
                            logResult('sync-result', result, 'success');
                        });
                    } else {
                        result += `\n📊 无需同步，token已一致`;
                        
                        // 更新检查时间
                        chrome.storage.local.set({
                            last_consistency_check: Date.now()
                        }, () => {
                            logResult('sync-result', result, 'success');
                        });
                    }
                });
                
            } catch (error) {
                logResult('sync-result', `强制一致性检查失败: ${error.message}`, 'error');
            }
        }

        // 其他测试函数的简化实现...
        function testMissingLocalStorage() {
            // 清除localStorage中的token
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (key) {
                    try {
                        const decryptedKey = Base64EncoderDecoder.decrypt(key);
                        if (decryptedKey === 'auth_token') {
                            localStorage.removeItem(key);
                        }
                    } catch (error) {
                        // 忽略解密错误
                    }
                }
            }
            
            logResult('consistency-result', '✅ 已清除localStorage中的token\n📊 预期结果: 一致性检查应该跳过', 'info');
        }

        function testMissingCache() {
            const cacheKeys = ['cached_auth_token', 'token_timestamp', 'token_expiry', 'last_token_check', 'token_hash'];
            chrome.storage.local.remove(cacheKeys, () => {
                logResult('consistency-result', '✅ 已清除插件缓存\n📊 预期结果: 一致性检查应该检测到缓存缺失', 'info');
            });
        }

        function clearAllCaches() {
            // 清除localStorage
            testMissingLocalStorage();
            
            // 清除Chrome存储
            chrome.storage.local.clear(() => {
                logResult('sync-result', '✅ 已清除所有缓存（localStorage + Chrome存储）', 'success');
            });
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            setTimeout(checkCurrentStatus, 500);
        });
    </script>
</body>
</html>
