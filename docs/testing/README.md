# 测试指南

本文档提供了TypeScript插件的完整测试指南，包括测试环境搭建、测试方法和验证工具的使用。

## 🧪 测试概述

### 测试目标
- **功能验证**: 确保所有功能正常工作
- **性能测试**: 验证系统性能指标
- **兼容性测试**: 确保跨浏览器和域名兼容
- **安全测试**: 验证安全机制有效性

### 测试范围
- 身份验证系统
- Token一致性检测
- 缓存策略
- API调用
- 错误处理

## 🛠️ 测试环境

### 环境要求
- **浏览器**: Chrome 88+ (支持Chrome扩展API)
- **域名**: `https://service.smartdeerhr.com/` 或 `http://localhost:6004/`
- **权限**: Chrome扩展开发者模式

### 环境搭建
1. **加载插件**
   ```bash
   # 在Chrome中加载未打包的扩展
   chrome://extensions/ -> 开发者模式 -> 加载已解压的扩展程序
   ```

2. **配置测试域名**
   ```bash
   # 本地开发服务器
   npm run dev
   # 访问 http://localhost:6004/
   ```

3. **准备测试数据**
   ```javascript
   // 设置测试token到localStorage
   const testToken = 'test_token_' + Date.now();
   setEncryptedTokenInLocalStorage(testToken);
   ```

## 📋 测试工具

### 1. Token一致性检测测试
**文件**: `test-token-consistency.html`

#### 功能特性
- 一致性检测场景测试
- 同步功能验证
- 性能影响测试
- 调试工具集成

#### 使用方法
```html
<!-- 在目标域名下打开 -->
http://localhost:6004/test-token-consistency.html

<!-- 或在生产环境 -->
https://service.smartdeerhr.com/test-token-consistency.html
```

#### 测试场景
- ✅ 测试一致Token
- ⚠️ 测试不一致Token
- 📭 测试缺失localStorage
- 💾 测试缺失缓存
- 🔄 测试自动同步

### 2. 身份验证优化测试
**文件**: `test-auth-optimization.html`

#### 功能特性
- 缓存策略测试
- 智能刷新验证
- 错误处理测试
- 性能基准测试

#### 测试内容
- 内存缓存性能
- 持久化缓存验证
- Token刷新机制
- 重试策略测试

### 3. API调用测试
**文件**: `test-api-calls.html`

#### 功能特性
- request.postV2方法测试
- 认证流程验证
- 错误处理测试
- 兼容性验证

### 4. 自动化验证脚本
**文件**: `verify-auth-optimization.js`

#### 功能特性
- 自动化测试执行
- 结果报告生成
- 性能指标收集
- 错误诊断

#### 使用方法
```javascript
// 在浏览器控制台中运行
runAuthVerification().then(report => {
  console.log('测试报告:', report);
});

// 或在Node.js环境中
node verify-auth-optimization.js
```

## 🔍 测试方法

### 1. 功能测试

#### 基础功能测试
```javascript
// 1. 测试token获取
const token = await authTokenManager.getAuthToken();
console.log('Token获取:', token ? '成功' : '失败');

// 2. 测试缓存状态
const status = await authTokenManager.getCacheStatus();
console.log('缓存状态:', status);

// 3. 测试一致性检查
const result = await authTokenManager.forceTokenConsistencyCheck();
console.log('一致性检查:', result);
```

#### API调用测试
```javascript
// 测试request.postV2方法
const response = await request.postV2({
  data: {
    functionKey: 'test_endpoint',
    params: { test: true }
  }
});
console.log('API调用结果:', response);
```

### 2. 性能测试

#### 缓存性能测试
```javascript
// 内存缓存性能
const startTime = performance.now();
const token1 = await authTokenManager.getAuthToken();
const memoryTime = performance.now() - startTime;
console.log('内存缓存耗时:', memoryTime + 'ms');

// 持久化缓存性能
await authTokenManager.clearCache();
const startTime2 = performance.now();
const token2 = await authTokenManager.getAuthToken();
const storageTime = performance.now() - startTime2;
console.log('持久化缓存耗时:', storageTime + 'ms');
```

#### 一致性检查性能
```javascript
// 测试检查频率
const checkTimes = [];
for (let i = 0; i < 10; i++) {
  const start = performance.now();
  await authTokenManager.forceTokenConsistencyCheck();
  checkTimes.push(performance.now() - start);
}
console.log('平均检查耗时:', checkTimes.reduce((a, b) => a + b) / checkTimes.length + 'ms');
```

### 3. 兼容性测试

#### 跨域名测试
```javascript
// 在不同域名下测试
const domains = [
  'https://service.smartdeerhr.com/',
  'http://localhost:6004/',
  'https://example.com/' // 非目标域名
];

for (const domain of domains) {
  // 模拟在不同域名下的行为
  console.log(`测试域名: ${domain}`);
  // 执行测试...
}
```

#### 浏览器兼容性
- Chrome 88+
- Edge 88+
- Firefox (有限支持)

### 4. 安全测试

#### Token安全测试
```javascript
// 测试加密存储
const testToken = 'sensitive_token_data';
setEncryptedTokenInLocalStorage(testToken);

// 验证localStorage中的数据是否加密
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  const value = localStorage.getItem(key);
  console.log('存储数据:', { key, value });
  // 确认数据已加密，不包含明文token
}
```

#### 错误处理安全
```javascript
// 测试错误信息是否泄露敏感数据
try {
  await authTokenManager.getAuthToken();
} catch (error) {
  console.log('错误信息:', error.message);
  // 确认错误信息不包含敏感token数据
}
```

## 📊 测试报告

### 1. 测试结果记录

#### 功能测试结果
```
✅ Token获取: 通过
✅ 缓存机制: 通过
✅ 一致性检测: 通过
✅ 自动同步: 通过
✅ API调用: 通过
```

#### 性能测试结果
```
📊 内存缓存: < 10ms
📊 持久化缓存: < 100ms
📊 一致性检查: < 50ms
📊 自动同步: < 200ms
```

### 2. 问题追踪

#### 常见问题
1. **Token获取失败**
   - 原因: localStorage中无有效token
   - 解决: 确保用户已登录

2. **一致性检查不触发**
   - 原因: 不在目标域名或检查间隔未到
   - 解决: 检查域名和时间间隔

3. **缓存同步失败**
   - 原因: Chrome存储权限问题
   - 解决: 检查扩展权限配置

## 🚀 最佳实践

### 1. 测试策略
- **自动化优先**: 使用自动化脚本进行回归测试
- **场景覆盖**: 确保覆盖所有使用场景
- **性能基准**: 建立性能基准并持续监控
- **错误模拟**: 模拟各种错误情况

### 2. 测试流程
1. **单元测试**: 测试单个功能模块
2. **集成测试**: 测试模块间交互
3. **端到端测试**: 测试完整用户流程
4. **性能测试**: 验证性能指标
5. **安全测试**: 验证安全机制

### 3. 持续改进
- **定期回归**: 每次更新后执行完整测试
- **性能监控**: 持续监控性能指标
- **用户反馈**: 收集用户使用反馈
- **问题跟踪**: 建立问题跟踪机制

## 📋 相关文档

- [验证工具详解](./verification-tools.md)
- [性能测试指南](./performance.md)
- [故障排除指南](../troubleshooting/README.md)
- [开发指南](../development/README.md)

---

**维护者**: 测试团队  
**最后更新**: 2025-07-08
