# Token一致性检测机制

Token一致性检测是身份验证系统的重要组成部分，确保localStorage中的token与插件缓存中的token保持同步，避免因用户重新登录或token更新导致的认证问题。

## 🎯 设计目标

### 核心问题
1. **用户重新登录**: 用户在网站上重新登录后，localStorage中的token更新，但插件缓存中仍是旧token
2. **Token自动更新**: 网站自动刷新token时，插件缓存可能不同步
3. **多标签页问题**: 不同标签页的token状态可能不一致
4. **性能影响**: 频繁检查会影响插件性能

### 解决方案
- **智能检测间隔**: 避免频繁检查，平衡性能和及时性
- **自动同步机制**: 检测到不一致时自动同步最新token
- **Hash比较优化**: 使用hash快速比较，减少字符串比较开销
- **域名限制**: 仅在目标域名下进行检测，避免无效检查

## 🔍 检测机制

### 1. 智能检测触发

#### 触发条件
- 用户在允许的域名下（`https://service.smartdeerhr.com/` 或 `http://localhost:6004/`）
- 距离上次检查时间超过5分钟
- 调用`getAuthToken()`方法时自动触发

#### 检测间隔
```typescript
const TOKEN_CONSISTENCY_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟
```

### 2. 一致性比较

#### 比较方法
1. **直接字符串比较**: 最准确的比较方式
2. **Hash值比较**: 快速比较，减少性能开销
3. **缓存状态检查**: 检查缓存是否存在和有效

#### 比较逻辑
```typescript
private async compareTokens(localToken: string, cachedToken?: string): Promise<boolean> {
  if (!cachedToken) return false;
  
  // 直接比较
  if (localToken === cachedToken) return true;
  
  // Hash比较
  const cache = await this.getStorageCache();
  if (cache.token_hash) {
    const localTokenHash = this.generateTokenHash(localToken);
    return localTokenHash === cache.token_hash;
  }
  
  return false;
}
```

### 3. 自动同步流程

#### 同步步骤
1. **清除旧缓存**: 删除所有相关的缓存数据
2. **存储新token**: 将localStorage中的token存储到插件缓存
3. **更新内存缓存**: 同步更新内存中的缓存
4. **记录同步时间**: 更新最后检查时间

#### 同步实现
```typescript
private async syncTokenFromLocalStorage(newToken: string): Promise<void> {
  console.log('🔄 开始同步token到插件缓存...');
  
  // 清除旧缓存
  await this.clearCache();
  
  // 存储新token
  await this.setCacheWithExpiry(newToken);
  
  // 更新内存缓存
  this.updateMemoryCache(newToken);
  
  console.log('✅ Token同步完成');
}
```

## ⚡ 性能优化

### 1. 智能检测间隔

#### 避免频繁检查
- **时间间隔控制**: 5分钟内不重复检查
- **域名限制**: 仅在目标域名下检查
- **缓存检查时间**: 记录最后检查时间，避免重复检查

#### 检查时机优化
```typescript
private async checkTokenConsistencyIfNeeded(): Promise<void> {
  const cache = await this.getStorageCache();
  const now = Date.now();
  
  // 检查是否需要进行一致性检查
  if (cache.last_consistency_check && 
      (now - cache.last_consistency_check) < TOKEN_CONSISTENCY_CHECK_INTERVAL) {
    return; // 跳过检查
  }
  
  // 执行检查...
}
```

### 2. Hash优化

#### 快速比较
- **Token Hash**: 生成token的hash值用于快速比较
- **缓存Hash**: 存储hash值，避免重复计算
- **轻量级算法**: 使用简单高效的hash算法

#### Hash生成
```typescript
private generateTokenHash(token: string): string {
  let hash = 0;
  for (let i = 0; i < token.length; i++) {
    const char = token.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return hash.toString(36);
}
```

### 3. 异步处理

#### 非阻塞检查
- **异步执行**: 一致性检查不阻塞主流程
- **错误隔离**: 检查失败不影响正常功能
- **后台同步**: 同步操作在后台执行

## 🔧 配置和使用

### 1. 自动检测

#### 默认行为
```typescript
// 在getAuthToken()中自动触发
public async getAuthToken(): Promise<string> {
  // 在允许的域名下，首先检查token一致性
  const currentUrl = window.location.href;
  const isOnTargetDomain = ALLOWED_DOMAINS.some(domain => currentUrl.startsWith(domain));
  
  if (isOnTargetDomain) {
    await this.checkTokenConsistencyIfNeeded();
  }
  
  // 继续正常的token获取流程...
}
```

### 2. 手动检测

#### 强制检查
```typescript
// 手动触发一致性检查
const result = await authTokenManager.forceTokenConsistencyCheck();

console.log('检查结果:', {
  wasInconsistent: result.wasInconsistent,
  syncPerformed: result.syncPerformed,
  error: result.error
});
```

#### 返回值说明
- `wasInconsistent`: 是否检测到不一致
- `syncPerformed`: 是否执行了同步操作
- `error`: 错误信息（如果有）

### 3. 状态监控

#### 缓存状态检查
```typescript
const status = await authTokenManager.getCacheStatus();

console.log('一致性状态:', {
  lastConsistencyCheck: status.lastConsistencyCheck,
  needsConsistencyCheck: status.needsConsistencyCheck
});
```

## 🧪 测试验证

### 1. 测试场景

#### 一致性测试
- **一致Token**: localStorage和缓存中的token相同
- **不一致Token**: localStorage和缓存中的token不同
- **缺失Token**: localStorage或缓存中缺少token
- **损坏Token**: token格式错误或损坏

#### 性能测试
- **检测频率**: 验证5分钟间隔是否生效
- **响应时间**: 检测操作的耗时
- **内存使用**: 检测对内存使用的影响

### 2. 测试工具

#### 自动化测试
使用 `test-token-consistency.html` 进行全面测试：

```html
<!-- 测试一致性检测的各种场景 -->
<button onclick="testConsistentTokens()">测试一致Token</button>
<button onclick="testInconsistentTokens()">测试不一致Token</button>
<button onclick="forceConsistencyCheck()">强制一致性检查</button>
```

#### 手动验证
1. 在目标域名下登录
2. 修改localStorage中的token
3. 触发插件功能
4. 观察是否自动同步

## 📊 监控指标

### 1. 性能指标
- **检测频率**: 每5分钟最多1次
- **检测耗时**: < 50ms
- **同步耗时**: < 200ms

### 2. 功能指标
- **检测准确率**: 100%
- **同步成功率**: > 99%
- **错误恢复率**: 100%

## 🚨 故障排除

### 1. 常见问题

#### 检测不生效
- 检查是否在允许的域名下
- 确认检查间隔是否已过
- 查看控制台日志

#### 同步失败
- 检查localStorage中是否有有效token
- 确认Chrome存储权限
- 查看错误日志

### 2. 调试方法

#### 启用详细日志
```typescript
// 在控制台中查看详细日志
console.log('🔍 开始检查token一致性...');
console.log('⚠️ 检测到token不一致，开始同步...');
console.log('✅ Token同步完成');
```

#### 手动测试
```typescript
// 手动触发检测
await authTokenManager.forceTokenConsistencyCheck();

// 检查状态
const status = await authTokenManager.getCacheStatus();
console.log(status);
```

## 📋 相关文档

- [身份验证系统概述](./README.md)
- [缓存策略优化](./cache-strategy.md)
- [API使用指南](./api-guide.md)
- [测试指南](../testing/README.md)

---

**版本**: v2.0  
**最后更新**: 2025-07-08
