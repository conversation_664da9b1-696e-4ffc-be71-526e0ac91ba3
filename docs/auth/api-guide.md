# API使用指南

本文档提供了TypeScript插件身份验证系统的完整API使用指南，包括基本用法、高级功能和最佳实践。

## 🚀 快速开始

### 基本API调用
```typescript
import request from '@/utils/plugin-request';

// 基本 API 调用 - 认证会自动处理
async function fetchUserData() {
  try {
    const response = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_get_user_info',
        params: {
          userId: 123
        }
      }
    });
    
    console.log('用户数据:', response);
    return response;
  } catch (error) {
    console.error('获取用户数据失败:', error.message);
    throw error;
  }
}
```

### 带错误处理的API调用
```typescript
async function saveUserData(userData) {
  try {
    const response = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_save_user',
        params: userData
      }
    });
    
    console.log('✅ 保存成功:', response);
    return response;
  } catch (error) {
    // 错误信息已经是中文的，可以直接显示给用户
    if (error.message.includes('🔐 身份验证失败')) {
      // 认证错误 - 引导用户重新登录
      alert('请重新登录后再试');
      window.location.href = 'https://service.smartdeerhr.com/login';
    } else if (error.message.includes('🚫 权限不足')) {
      // 权限错误 - 提示联系管理员
      alert('您没有执行此操作的权限，请联系管理员');
    } else {
      // 其他错误
      alert(`操作失败: ${error.message}`);
    }
    throw error;
  }
}
```

## 🔐 认证状态管理

### 检查认证状态
```typescript
async function checkAuthenticationStatus() {
  try {
    const status = await authTokenManager.getCacheStatus();
    
    console.log('认证状态:', {
      内存缓存: status.hasMemoryCache ? '✅' : '❌',
      持久缓存: status.hasStorageCache ? '✅' : '❌',
      是否过期: status.isExpired ? '⚠️ 已过期' : '✅ 有效',
      剩余时间: status.timeUntilExpiry 
        ? `${Math.round(status.timeUntilExpiry / (1000 * 60 * 60))} 小时`
        : '无'
    });
    
    return status;
  } catch (error) {
    console.error('检查认证状态失败:', error);
    return null;
  }
}
```

### 手动刷新认证令牌
```typescript
async function refreshAuthentication() {
  try {
    console.log('🔄 正在刷新认证令牌...');
    await authTokenManager.clearCache();
    const token = await authTokenManager.getAuthToken();
    console.log('✅ 认证令牌刷新成功');
    return token;
  } catch (error) {
    console.error('❌ 刷新认证令牌失败:', error.message);
    throw error;
  }
}
```

### 清除认证缓存
```typescript
async function logout() {
  try {
    await authTokenManager.clearCache();
    console.log('✅ 已清除认证缓存');
    // 可以重定向到登录页面
    window.location.href = '/login';
  } catch (error) {
    console.error('❌ 清除认证缓存失败:', error);
  }
}
```

## 🔄 Token一致性检测

### 手动触发一致性检查
```typescript
async function checkTokenConsistency() {
  try {
    const result = await authTokenManager.forceTokenConsistencyCheck();
    
    console.log('一致性检查结果:', {
      检查时间: new Date().toLocaleString(),
      是否不一致: result.wasInconsistent ? '是' : '否',
      是否同步: result.syncPerformed ? '是' : '否',
      错误信息: result.error || '无'
    });
    
    return result;
  } catch (error) {
    console.error('一致性检查失败:', error);
    return null;
  }
}
```

### 获取一致性状态
```typescript
async function getConsistencyStatus() {
  try {
    const status = await authTokenManager.getCacheStatus();
    
    console.log('一致性状态:', {
      最后检查时间: status.lastConsistencyCheck 
        ? new Date(status.lastConsistencyCheck).toLocaleString() 
        : '从未检查',
      需要检查: status.needsConsistencyCheck ? '是' : '否'
    });
    
    return status;
  } catch (error) {
    console.error('获取一致性状态失败:', error);
    return null;
  }
}
```

## 📱 实际应用场景

### 用户登录检查
```typescript
async function checkUserLogin() {
  try {
    // 检查认证状态
    const authStatus = await authTokenManager.getCacheStatus();
    
    if (!authStatus || authStatus.isExpired) {
      console.log('⚠️ 认证已过期，尝试刷新...');
      await authTokenManager.clearCache();
      await authTokenManager.getAuthToken();
    }
    
    // 验证登录状态
    const userInfo = await request.postV2({
      data: {
        functionKey: 'rcn_plugin_check_login',
        params: {}
      }
    });
    
    return userInfo;
  } catch (error) {
    console.error('登录检查失败:', error.message);
    return null;
  }
}
```

### 批量API调用
```typescript
async function batchApiCalls(requests) {
  const results = [];
  
  for (const req of requests) {
    try {
      const response = await request.postV2({
        data: {
          functionKey: req.functionKey,
          params: req.params
        }
      });
      
      results.push({ success: true, data: response });
    } catch (error) {
      console.error(`API 调用失败 (${req.functionKey}):`, error.message);
      results.push({ success: false, error: error.message });
      
      // 如果是认证错误，停止后续调用
      if (error.message.includes('🔐 身份验证失败')) {
        console.log('❌ 认证失败，停止批量调用');
        break;
      }
    }
  }
  
  return results;
}
```

### 自动重试的API调用
```typescript
async function reliableApiCall(functionKey, params, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await request.postV2({
        data: { functionKey, params }
      });
      
      console.log(`✅ API 调用成功 (尝试 ${attempt}/${maxRetries})`);
      return response;
    } catch (error) {
      console.error(`❌ API 调用失败 (尝试 ${attempt}/${maxRetries}):`, error.message);
      
      // 如果是认证错误，尝试刷新令牌
      if (error.message.includes('🔐 身份验证失败') && attempt < maxRetries) {
        console.log('🔄 尝试刷新认证令牌...');
        try {
          await authTokenManager.clearCache();
          await authTokenManager.getAuthToken();
          continue; // 重试
        } catch (refreshError) {
          console.error('❌ 刷新令牌失败:', refreshError.message);
        }
      }
      
      // 最后一次尝试失败，抛出错误
      if (attempt === maxRetries) {
        throw error;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

## 🚨 错误处理

### 统一错误处理函数
```typescript
function handleApiError(error, context = '') {
  const prefix = context ? `[${context}] ` : '';
  
  if (error.message.includes('🔐 身份验证失败')) {
    console.error(`${prefix}认证失败:`, error.message);
    return 'AUTH_ERROR';
  } else if (error.message.includes('🚫 权限不足')) {
    console.error(`${prefix}权限不足:`, error.message);
    return 'PERMISSION_ERROR';
  } else if (error.message.includes('🌐 网络错误')) {
    console.error(`${prefix}网络错误:`, error.message);
    return 'NETWORK_ERROR';
  } else {
    console.error(`${prefix}未知错误:`, error.message);
    return 'UNKNOWN_ERROR';
  }
}
```

### 用户友好的错误提示
```typescript
function showUserFriendlyError(error) {
  const errorType = handleApiError(error);
  
  switch (errorType) {
    case 'AUTH_ERROR':
      showNotification('请重新登录', 'warning');
      break;
    case 'PERMISSION_ERROR':
      showNotification('您没有执行此操作的权限', 'error');
      break;
    case 'NETWORK_ERROR':
      showNotification('网络连接异常，请检查网络后重试', 'error');
      break;
    default:
      showNotification('操作失败，请稍后重试', 'error');
  }
}

function showNotification(message, type) {
  // 实现您的通知系统
  console.log(`[${type.toUpperCase()}] ${message}`);
}
```

## 📊 调试和监控

### 启用详细日志
```typescript
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  // 监听认证状态变化
  setInterval(async () => {
    const status = await authTokenManager.getCacheStatus();
    console.log('🔍 认证状态监控:', status);
  }, 60000); // 每分钟检查一次
}
```

### 性能监控
```typescript
async function monitoredApiCall(functionKey, params) {
  const startTime = Date.now();
  
  try {
    const response = await request.postV2({
      data: { functionKey, params }
    });
    
    const duration = Date.now() - startTime;
    console.log(`📊 API 调用性能: ${functionKey} - ${duration}ms`);
    
    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`📊 API 调用失败: ${functionKey} - ${duration}ms - ${error.message}`);
    throw error;
  }
}
```

## ✅ 最佳实践

### 推荐做法
1. **总是使用 try-catch** 包装 API 调用
2. **检查错误类型** 并提供相应的用户提示
3. **在应用启动时** 检查认证状态
4. **定期监控** 认证状态（可选）
5. **提供手动刷新** 认证的选项

### 避免做法
1. **不要忽略错误** - 总是处理可能的异常
2. **不要频繁调用** getCacheStatus - 它已经有内部缓存
3. **不要手动操作** chrome.storage - 使用提供的 API
4. **不要在生产环境** 输出敏感的认证信息

### 配置建议
1. **设置合适的超时时间** 
2. **配置重试策略**
3. **实现用户友好的错误界面**
4. **添加认证状态指示器**

## 📋 相关文档

- [身份验证系统概述](./README.md)
- [Token一致性检测](./token-consistency.md)
- [缓存策略优化](./cache-strategy.md)
- [测试指南](../testing/README.md)

---

**版本**: v2.0  
**最后更新**: 2025-07-08
