# 身份验证系统概述

TypeScript插件的身份验证系统是一个高度优化的、多层缓存的认证解决方案，旨在提供安全、高效、用户友好的身份验证体验。

## 🏗️ 系统架构

### 核心组件

1. **AuthTokenManager** - 身份验证令牌管理器
   - 单例模式设计
   - 多层缓存策略
   - 智能刷新机制
   - Token一致性检测

2. **Base64EncoderDecoder** - 加密解密工具
   - 自定义Base64编码算法
   - 块化处理机制
   - 安全的数据存储

3. **Chrome Storage API** - 持久化存储
   - 跨会话数据保持
   - 安全的本地存储
   - 自动过期管理

## 🔄 工作流程

### 1. Token获取流程
```
用户请求 → 检查内存缓存 → 检查持久化缓存 → 从localStorage获取 → 存储到缓存
```

### 2. 一致性检测流程
```
域名检查 → 智能检测间隔 → 比较localStorage与缓存 → 自动同步 → 更新检查时间
```

### 3. 缓存刷新流程
```
过期预警 → 后台刷新 → 更新缓存 → 通知应用 → 记录日志
```

## 🎯 核心特性

### 多层缓存系统
- **内存缓存**: 页面内快速访问，响应时间 < 10ms
- **持久化缓存**: Chrome存储，24小时有效期
- **源数据**: localStorage中的加密token

### 智能刷新机制
- **预警阈值**: Token过期前2小时开始预警
- **后台刷新**: 不阻塞当前请求的后台更新
- **重试策略**: 指数退避，最多3次重试

### Token一致性检测
- **智能检测**: 5分钟间隔的智能检测
- **自动同步**: 检测到不一致时自动同步
- **性能优化**: 避免频繁检查影响性能

## 🔒 安全特性

### 数据加密
- **Base64加密**: 自定义算法加密存储
- **密钥保护**: 敏感信息不明文存储
- **自动清理**: 过期数据自动清除

### 域名安全
- **白名单机制**: 严格限制允许的域名
- **跨域保护**: 防止未授权域名访问
- **降级策略**: 非目标域名的安全降级

### 错误处理
- **安全日志**: 不泄露敏感信息的错误记录
- **用户友好**: 中文错误提示和解决建议
- **自动恢复**: 损坏缓存的自动清理和恢复

## 📊 性能指标

### 缓存性能
- **内存缓存命中率**: 页面内 100%
- **持久化缓存命中率**: 24小时内 95%+
- **localStorage遍历**: 仅在缓存失效时执行

### 响应时间
- **内存缓存**: < 10ms
- **持久化缓存**: < 100ms
- **localStorage获取**: < 500ms

### 网络优化
- **减少认证请求**: 缓存有效期内零额外请求
- **智能重试**: 失败时指数退避，避免频繁重试
- **并发安全**: 多个同时请求共享同一认证过程

## 🔧 配置参数

### 时间配置
```typescript
const TOKEN_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存
const TOKEN_REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // 2小时预警
const TOKEN_CONSISTENCY_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查
```

### 域名配置
```typescript
const ALLOWED_DOMAINS = [
  'https://service.smartdeerhr.com/',
  'http://localhost:6004/'
];
```

### 重试配置
```typescript
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAYS = [1000, 2000, 4000]; // 指数退避延迟
```

## 🚀 使用示例

### 基本使用
```typescript
// 自动处理身份验证，无需额外代码
const response = await request.postV2({
  data: {
    functionKey: 'getUserInfo',
    params: { userId: 123 }
  }
});
```

### 高级使用
```typescript
// 检查缓存状态
const status = await authTokenManager.getCacheStatus();
console.log('缓存状态:', status);

// 手动触发一致性检查
const result = await authTokenManager.forceTokenConsistencyCheck();
if (result.wasInconsistent) {
  console.log('检测到token不一致，已自动同步');
}

// 清除缓存（调试用）
await authTokenManager.clearCache();
```

## 📋 相关文档

- [Token一致性检测详解](./token-consistency.md)
- [缓存策略优化指南](./cache-strategy.md)
- [API使用指南](./api-guide.md)
- [故障排除指南](../troubleshooting/auth-issues.md)

## 🔄 版本历史

- **v2.0** (2025-07-08): 添加Token一致性检测机制
- **v1.1** (2025-07-08): 优化缓存策略和智能刷新
- **v1.0** (2025-07-08): 基础身份验证系统实现

---

**维护者**: 开发团队  
**最后更新**: 2025-07-08
