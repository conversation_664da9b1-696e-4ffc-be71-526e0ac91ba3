# TypeScript插件文档中心

欢迎来到TypeScript插件的文档中心。这里包含了插件开发、使用和维护的所有相关文档。

## 📚 文档目录

### 🔐 身份验证系统
- [身份验证系统概述](./auth/README.md) - 身份验证系统的整体架构和设计
- [Token一致性检测](./auth/token-consistency.md) - Token一致性检测机制说明
- [缓存策略优化](./auth/cache-strategy.md) - 缓存策略的优化和配置
- [API使用指南](./auth/api-guide.md) - 身份验证相关API的使用方法

### 🧪 测试和验证
- [测试指南](./testing/README.md) - 测试环境搭建和测试方法
- [验证工具](./testing/verification-tools.md) - 各种验证工具的使用说明
- [性能测试](./testing/performance.md) - 性能测试方法和基准

### 📖 开发指南
- [开发环境搭建](./development/setup.md) - 开发环境的配置和搭建
- [代码规范](./development/coding-standards.md) - 代码编写规范和最佳实践
- [调试指南](./development/debugging.md) - 调试方法和常见问题解决

### 📋 项目历史
- [优化历史](./history/optimization-history.md) - 项目优化的历史记录
- [变更日志](./history/changelog.md) - 详细的变更记录
- [版本说明](./history/version-notes.md) - 各版本的功能说明

## 🚀 快速开始

### 基本使用
```typescript
// 基本的API调用，无需修改现有代码
const response = await request.postV2({
  data: {
    functionKey: 'your_endpoint',
    params: { id: 123 }
  }
});
```

### 高级功能
```typescript
// 检查身份验证状态
const status = await authTokenManager.getCacheStatus();
console.log('缓存状态:', status);

// 手动触发一致性检查
const result = await authTokenManager.forceTokenConsistencyCheck();
console.log('一致性检查结果:', result);
```

## 🔧 配置说明

### 环境配置
- **开发环境**: `http://localhost:6004/`
- **生产环境**: `https://service.smartdeerhr.com/`

### 缓存配置
- **Token缓存时长**: 24小时
- **刷新预警阈值**: 2小时
- **一致性检查间隔**: 5分钟

## 📞 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看相关文档是否有解决方案
2. 检查[常见问题](./troubleshooting/faq.md)
3. 使用[调试工具](./testing/verification-tools.md)进行诊断
4. 联系开发团队获取支持

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](../LICENSE) 文件。

---

**最后更新**: 2025-07-08  
**文档版本**: v2.0  
**插件版本**: v2.0
