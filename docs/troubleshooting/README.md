# 故障排除指南

本文档提供了TypeScript插件身份验证系统常见问题的诊断和解决方案。

## 🚨 常见问题

### 1. 身份验证失败

#### 问题描述
- API调用返回"🔐 身份验证失败"错误
- 用户已登录但插件无法获取token

#### 可能原因
1. **localStorage中无有效token**
2. **插件缓存与localStorage不同步**
3. **token已过期但未及时刷新**
4. **域名不在允许列表中**

#### 解决步骤

**步骤1：检查当前域名**
```javascript
console.log('当前域名:', window.location.href);
// 确认是否为允许的域名：
// - https://service.smartdeerhr.com/
// - http://localhost:6004/
```

**步骤2：检查localStorage中的token**
```javascript
// 在浏览器控制台中运行
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  console.log('localStorage key:', key);
  
  try {
    const decryptedKey = Base64EncoderDecoder.decrypt(key);
    if (decryptedKey === 'auth_token') {
      const value = localStorage.getItem(key);
      const decryptedValue = Base64EncoderDecoder.decrypt(value);
      console.log('找到token:', decryptedValue.substring(0, 30) + '...');
    }
  } catch (error) {
    // 忽略解密错误
  }
}
```

**步骤3：检查插件缓存状态**
```javascript
// 检查缓存状态
const status = await authTokenManager.getCacheStatus();
console.log('缓存状态:', status);
```

**步骤4：强制刷新token**
```javascript
// 清除缓存并重新获取
await authTokenManager.clearCache();
const newToken = await authTokenManager.getAuthToken();
console.log('新token获取:', newToken ? '成功' : '失败');
```

### 2. Token一致性问题

#### 问题描述
- localStorage中的token与插件缓存不一致
- 用户重新登录后插件仍使用旧token

#### 诊断方法
```javascript
// 检查一致性状态
const result = await authTokenManager.forceTokenConsistencyCheck();
console.log('一致性检查结果:', result);

if (result.wasInconsistent) {
  console.log('检测到不一致，已自动同步');
} else {
  console.log('token一致，无需同步');
}
```

#### 解决方案
1. **自动同步**：系统会自动检测并同步
2. **手动触发**：使用`forceTokenConsistencyCheck()`
3. **清除重置**：清除所有缓存重新开始

### 3. 缓存相关问题

#### 问题描述
- 缓存数据损坏或不一致
- Chrome存储权限问题

#### 诊断步骤

**检查Chrome存储权限**
```javascript
// 检查存储权限
chrome.storage.local.get(['test'], (result) => {
  if (chrome.runtime.lastError) {
    console.error('存储权限错误:', chrome.runtime.lastError);
  } else {
    console.log('存储权限正常');
  }
});
```

**检查缓存数据完整性**
```javascript
const cacheKeys = [
  'cached_auth_token',
  'token_timestamp', 
  'token_expiry',
  'last_token_check',
  'last_consistency_check',
  'token_hash'
];

chrome.storage.local.get(cacheKeys, (cache) => {
  console.log('缓存数据:', cache);
  
  // 检查数据完整性
  if (cache.cached_auth_token && !cache.token_timestamp) {
    console.warn('⚠️ 缓存数据不完整，建议清除重建');
  }
});
```

**清除损坏的缓存**
```javascript
// 完全清除缓存
await authTokenManager.clearCache();
console.log('✅ 缓存已清除');

// 重新获取token
const newToken = await authTokenManager.getAuthToken();
console.log('重新获取token:', newToken ? '成功' : '失败');
```

### 4. 性能问题

#### 问题描述
- API调用响应缓慢
- 频繁的认证检查影响性能

#### 诊断方法

**检查缓存命中率**
```javascript
// 测试内存缓存性能
const startTime = performance.now();
const token1 = await authTokenManager.getAuthToken();
const memoryTime = performance.now() - startTime;
console.log('内存缓存耗时:', memoryTime + 'ms');

// 测试持久化缓存性能
await authTokenManager.clearMemoryCache(); // 假设有此方法
const startTime2 = performance.now();
const token2 = await authTokenManager.getAuthToken();
const storageTime = performance.now() - startTime2;
console.log('持久化缓存耗时:', storageTime + 'ms');
```

**检查一致性检查频率**
```javascript
// 监控一致性检查频率
let checkCount = 0;
const originalCheck = authTokenManager.checkTokenConsistencyIfNeeded;
authTokenManager.checkTokenConsistencyIfNeeded = async function() {
  checkCount++;
  console.log(`一致性检查次数: ${checkCount}`);
  return originalCheck.call(this);
};
```

#### 优化建议
1. **避免频繁调用**：不要在短时间内多次调用`getAuthToken()`
2. **使用缓存状态**：先检查`getCacheStatus()`再决定是否需要获取token
3. **合理的检查间隔**：一致性检查间隔为5分钟，避免手动频繁触发

### 5. 网络相关问题

#### 问题描述
- 网络请求失败
- 跨域问题

#### 诊断方法
```javascript
// 检查网络连接
try {
  const response = await fetch('https://service.smartdeerhr.com/health', {
    method: 'GET',
    mode: 'cors'
  });
  console.log('网络连接正常:', response.ok);
} catch (error) {
  console.error('网络连接问题:', error);
}
```

#### 解决方案
1. **检查网络连接**
2. **确认域名配置**
3. **检查CORS设置**
4. **使用重试机制**

## 🛠️ 调试工具

### 1. 综合诊断脚本
```javascript
async function runDiagnostics() {
  console.log('🔍 开始系统诊断...');
  
  // 1. 检查当前环境
  console.log('📍 当前域名:', window.location.href);
  
  // 2. 检查缓存状态
  const status = await authTokenManager.getCacheStatus();
  console.log('💾 缓存状态:', status);
  
  // 3. 检查localStorage
  let localToken = null;
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    try {
      const decryptedKey = Base64EncoderDecoder.decrypt(key);
      if (decryptedKey === 'auth_token') {
        const value = localStorage.getItem(key);
        const decryptedValue = Base64EncoderDecoder.decrypt(value);
        localToken = decryptedValue;
        break;
      }
    } catch (error) {
      continue;
    }
  }
  console.log('📱 localStorage token:', localToken ? '存在' : '不存在');
  
  // 4. 检查一致性
  const consistencyResult = await authTokenManager.forceTokenConsistencyCheck();
  console.log('🔄 一致性检查:', consistencyResult);
  
  // 5. 测试API调用
  try {
    const testResponse = await request.postV2({
      data: {
        functionKey: 'test_endpoint',
        params: {}
      }
    });
    console.log('🌐 API调用测试: 成功');
  } catch (error) {
    console.log('🌐 API调用测试: 失败 -', error.message);
  }
  
  console.log('✅ 诊断完成');
}

// 运行诊断
runDiagnostics();
```

### 2. 性能监控脚本
```javascript
function startPerformanceMonitoring() {
  const metrics = {
    apiCalls: 0,
    totalTime: 0,
    errors: 0,
    cacheHits: 0
  };
  
  // 监控API调用
  const originalPostV2 = request.postV2;
  request.postV2 = async function(options) {
    const startTime = performance.now();
    metrics.apiCalls++;
    
    try {
      const result = await originalPostV2.call(this, options);
      metrics.totalTime += performance.now() - startTime;
      return result;
    } catch (error) {
      metrics.errors++;
      metrics.totalTime += performance.now() - startTime;
      throw error;
    }
  };
  
  // 定期报告
  setInterval(() => {
    if (metrics.apiCalls > 0) {
      console.log('📊 性能报告:', {
        平均响应时间: `${(metrics.totalTime / metrics.apiCalls).toFixed(2)}ms`,
        总调用次数: metrics.apiCalls,
        错误次数: metrics.errors,
        成功率: `${((metrics.apiCalls - metrics.errors) / metrics.apiCalls * 100).toFixed(1)}%`
      });
    }
  }, 60000); // 每分钟报告一次
}

// 启动监控
startPerformanceMonitoring();
```

### 3. 错误追踪脚本
```javascript
function setupErrorTracking() {
  const errors = [];
  
  // 捕获未处理的错误
  window.addEventListener('error', (event) => {
    errors.push({
      type: 'JavaScript Error',
      message: event.message,
      filename: event.filename,
      line: event.lineno,
      timestamp: new Date().toISOString()
    });
  });
  
  // 捕获Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    errors.push({
      type: 'Unhandled Promise Rejection',
      message: event.reason.toString(),
      timestamp: new Date().toISOString()
    });
  });
  
  // 导出错误报告
  window.exportErrorReport = function() {
    console.log('📋 错误报告:', errors);
    return errors;
  };
}

// 启动错误追踪
setupErrorTracking();
```

## 📞 获取帮助

### 1. 自助诊断
1. 运行综合诊断脚本
2. 检查浏览器控制台错误
3. 使用测试页面验证功能

### 2. 收集信息
在寻求帮助时，请提供：
- 浏览器版本和类型
- 当前域名
- 错误信息截图
- 诊断脚本输出结果

### 3. 联系支持
- 查看[API使用指南](../auth/api-guide.md)
- 参考[认证令牌调试指南](./auth-debug-guide.md)
- 参考[测试指南](../testing/README.md)
- 联系开发团队

---

**维护者**: 支持团队  
**最后更新**: 2025-07-08
