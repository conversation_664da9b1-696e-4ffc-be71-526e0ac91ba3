# 认证令牌调试指南

本文档提供了TypeScript插件认证令牌的详细调试方法和问题修复指南。

## 🔧 问题修复说明

### 1. 令牌存储问题修复

#### 问题描述
- 收到的令牌格式畸形：`Nn0=****NTEyMDE5MTc1MTg3VGltZSI6InN0YXJ0MDAwMDAsOjI1OTIwcGlyZWQiViIsImV4OGduRlV6Rm9XN0xvakVEY1p2VjRXWkxlN1dTV1NJcFJuTVd0NDVObnRXaTJ5enVpcHk2bGpMa21aaDZseDllZDczdmRlemNpQk9oRFExYlhyMWF1U2dQc2dybmV3NG9FWVlLV0o2SVFNY2N4RUtvT3g4VlZrVEhqOThKamlldWUiOiJ5IiwidmFsX3Rva2VuOiJhdXRoeyJrZXki`
- 令牌解析和存储逻辑不一致

#### 修复内容
1. **改进令牌解析逻辑** - 增强了对不同令牌格式的处理能力
2. **添加令牌验证** - 验证令牌格式是否有效，过滤损坏的令牌
3. **统一存储逻辑** - 确保存储和获取的令牌格式一致
4. **增强错误处理** - 更详细的错误日志和异常处理
5. **改进调试信息** - 添加更多调试日志帮助排查问题

### 2. 背景控制台命令

#### 新增功能
- 一键清除认证令牌的控制台命令
- 通过插件通信系统清除令牌
- 完整的调试命令集

## 🛠️ 使用方法

### 方法一：浏览器控制台命令（推荐）

在任何页面打开浏览器开发者工具控制台，使用以下命令：

#### 基本调试命令
```javascript
// 1. 检查认证状态
await authTokenManager.getCacheStatus();

// 2. 获取当前令牌
await authTokenManager.getAuthToken();

// 3. 清除所有缓存
await authTokenManager.clearCache();

// 4. 强制一致性检查
await authTokenManager.forceTokenConsistencyCheck();
```

#### 高级调试命令
```javascript
// 检查localStorage中的加密令牌
function checkLocalStorageTokens() {
  console.log('🔍 检查localStorage中的令牌...');
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (!key) continue;
    
    try {
      const decryptedKey = Base64EncoderDecoder.decrypt(key);
      if (decryptedKey === 'auth_token') {
        const value = localStorage.getItem(key);
        const decryptedValue = Base64EncoderDecoder.decrypt(value);
        console.log('✅ 找到有效令牌:', decryptedValue.substring(0, 50) + '...');
        return decryptedValue;
      }
    } catch (error) {
      console.log('⚠️ 解密失败的key:', key);
    }
  }
  
  console.log('❌ 未找到有效令牌');
  return null;
}

// 运行检查
checkLocalStorageTokens();
```

#### 缓存诊断命令
```javascript
// 完整的缓存诊断
async function fullCacheDiagnostic() {
  console.log('🔍 开始完整缓存诊断...');
  
  // 1. 检查内存缓存
  console.log('1️⃣ 检查内存缓存...');
  // 注意：内存缓存在页面刷新后会丢失
  
  // 2. 检查Chrome存储
  console.log('2️⃣ 检查Chrome存储...');
  const cacheKeys = [
    'cached_auth_token',
    'token_timestamp',
    'token_expiry',
    'last_token_check',
    'last_consistency_check',
    'token_hash'
  ];
  
  chrome.storage.local.get(cacheKeys, (cache) => {
    console.log('Chrome存储内容:', cache);
    
    if (cache.cached_auth_token) {
      console.log('✅ 找到缓存令牌:', cache.cached_auth_token.substring(0, 30) + '...');
      console.log('📅 令牌时间戳:', new Date(cache.token_timestamp).toLocaleString());
      console.log('⏰ 过期时间:', new Date(cache.token_expiry).toLocaleString());
    } else {
      console.log('❌ 未找到缓存令牌');
    }
  });
  
  // 3. 检查localStorage
  console.log('3️⃣ 检查localStorage...');
  const localToken = checkLocalStorageTokens();
  
  // 4. 比较一致性
  console.log('4️⃣ 检查一致性...');
  setTimeout(async () => {
    const result = await authTokenManager.forceTokenConsistencyCheck();
    console.log('一致性检查结果:', result);
  }, 1000);
}

// 运行完整诊断
fullCacheDiagnostic();
```

### 方法二：插件后台控制台

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **找到TypeScript插件，点击"检查视图"中的"背景页"**

3. **在后台控制台中使用以下命令：**

#### 后台调试命令
```javascript
// 清除所有认证数据
async function clearAllAuthData() {
  console.log('🧹 开始清除所有认证数据...');
  
  try {
    // 清除Chrome存储
    await new Promise((resolve) => {
      chrome.storage.local.clear(() => {
        console.log('✅ Chrome存储已清除');
        resolve();
      });
    });
    
    console.log('✅ 所有认证数据已清除');
  } catch (error) {
    console.error('❌ 清除失败:', error);
  }
}

// 运行清除
clearAllAuthData();
```

```javascript
// 检查存储权限
function checkStoragePermissions() {
  console.log('🔐 检查存储权限...');
  
  chrome.storage.local.set({test: 'test_value'}, () => {
    if (chrome.runtime.lastError) {
      console.error('❌ 存储权限错误:', chrome.runtime.lastError);
    } else {
      console.log('✅ 存储权限正常');
      
      // 清除测试数据
      chrome.storage.local.remove('test', () => {
        console.log('🧹 测试数据已清除');
      });
    }
  });
}

// 检查权限
checkStoragePermissions();
```

### 方法三：网页端调试

在目标域名（`https://service.smartdeerhr.com/` 或 `http://localhost:6004/`）下：

#### 网页端调试命令
```javascript
// 模拟用户登录后的令牌设置
function simulateUserLogin() {
  console.log('🔄 模拟用户登录...');
  
  // 生成测试令牌
  const testToken = 'test_token_' + Date.now() + '_' + Math.random().toString(36).substring(2);
  
  // 加密并存储到localStorage
  const encryptedKey = Base64EncoderDecoder.encrypt('auth_token');
  const tokenData = JSON.stringify({ value: testToken });
  const encryptedValue = Base64EncoderDecoder.encrypt(tokenData);
  
  localStorage.setItem(encryptedKey, encryptedValue);
  
  console.log('✅ 测试令牌已设置:', testToken);
  console.log('🔑 加密key:', encryptedKey);
  console.log('🔒 加密value:', encryptedValue);
  
  return testToken;
}

// 运行模拟登录
simulateUserLogin();
```

```javascript
// 验证令牌一致性
async function verifyTokenConsistency() {
  console.log('🔍 验证令牌一致性...');
  
  // 获取localStorage中的令牌
  const localToken = checkLocalStorageTokens();
  
  // 获取插件缓存中的令牌
  const cachedToken = await authTokenManager.getAuthToken();
  
  console.log('📱 localStorage令牌:', localToken ? localToken.substring(0, 30) + '...' : '无');
  console.log('💾 缓存令牌:', cachedToken ? cachedToken.substring(0, 30) + '...' : '无');
  
  if (localToken && cachedToken) {
    const isConsistent = localToken === cachedToken;
    console.log('🔄 一致性状态:', isConsistent ? '✅ 一致' : '❌ 不一致');
    
    if (!isConsistent) {
      console.log('🔧 触发自动同步...');
      const result = await authTokenManager.forceTokenConsistencyCheck();
      console.log('同步结果:', result);
    }
  } else {
    console.log('⚠️ 令牌缺失，无法比较一致性');
  }
}

// 运行一致性验证
verifyTokenConsistency();
```

## 🚨 常见问题解决

### 1. 令牌格式错误
```javascript
// 检查并修复令牌格式
function fixTokenFormat() {
  console.log('🔧 检查令牌格式...');
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (!key) continue;
    
    try {
      const decryptedKey = Base64EncoderDecoder.decrypt(key);
      if (decryptedKey === 'auth_token') {
        const value = localStorage.getItem(key);
        const decryptedValue = Base64EncoderDecoder.decrypt(value);
        
        // 检查是否为有效的JSON格式
        try {
          const tokenData = JSON.parse(decryptedValue);
          console.log('✅ 令牌格式正确:', tokenData);
        } catch (jsonError) {
          console.log('⚠️ 令牌不是JSON格式，尝试直接使用:', decryptedValue);
          
          // 重新格式化并存储
          const newTokenData = JSON.stringify({ value: decryptedValue });
          const newEncryptedValue = Base64EncoderDecoder.encrypt(newTokenData);
          localStorage.setItem(key, newEncryptedValue);
          
          console.log('✅ 令牌格式已修复');
        }
      }
    } catch (error) {
      console.log('❌ 处理key失败:', key, error);
    }
  }
}

// 运行格式修复
fixTokenFormat();
```

### 2. 缓存同步问题
```javascript
// 强制重建缓存
async function forceRebuildCache() {
  console.log('🔄 强制重建缓存...');
  
  try {
    // 1. 清除所有缓存
    await authTokenManager.clearCache();
    console.log('✅ 缓存已清除');
    
    // 2. 重新获取令牌
    const newToken = await authTokenManager.getAuthToken();
    console.log('✅ 新令牌已获取:', newToken ? newToken.substring(0, 30) + '...' : '获取失败');
    
    // 3. 验证缓存状态
    const status = await authTokenManager.getCacheStatus();
    console.log('📊 新缓存状态:', status);
    
  } catch (error) {
    console.error('❌ 重建缓存失败:', error);
  }
}

// 运行重建
forceRebuildCache();
```

### 3. 权限问题
```javascript
// 检查和修复权限问题
function checkAndFixPermissions() {
  console.log('🔐 检查权限问题...');
  
  // 检查Chrome存储权限
  if (typeof chrome !== 'undefined' && chrome.storage) {
    console.log('✅ Chrome存储API可用');
    
    chrome.storage.local.get(['test'], (result) => {
      if (chrome.runtime.lastError) {
        console.error('❌ 存储权限错误:', chrome.runtime.lastError);
        console.log('💡 解决方案: 重新加载插件或检查manifest.json权限配置');
      } else {
        console.log('✅ 存储权限正常');
      }
    });
  } else {
    console.error('❌ Chrome存储API不可用');
    console.log('💡 解决方案: 确保在Chrome扩展环境中运行');
  }
  
  // 检查localStorage权限
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    console.log('✅ localStorage权限正常');
  } catch (error) {
    console.error('❌ localStorage权限错误:', error);
    console.log('💡 解决方案: 检查浏览器隐私设置或无痕模式');
  }
}

// 运行权限检查
checkAndFixPermissions();
```

## 📋 调试检查清单

### 基础检查
- [ ] 确认在正确的域名下（`https://service.smartdeerhr.com/` 或 `http://localhost:6004/`）
- [ ] 检查Chrome扩展是否正确加载
- [ ] 确认用户已登录并有有效的localStorage令牌

### 缓存检查
- [ ] 检查内存缓存状态
- [ ] 检查Chrome存储内容
- [ ] 验证令牌一致性
- [ ] 测试缓存刷新机制

### 权限检查
- [ ] 验证Chrome存储权限
- [ ] 检查localStorage访问权限
- [ ] 确认插件manifest权限配置

### 功能检查
- [ ] 测试API调用功能
- [ ] 验证错误处理机制
- [ ] 检查自动同步功能
- [ ] 测试性能指标

## 📞 获取更多帮助

如果以上调试方法无法解决问题，请：

1. 收集调试信息（运行完整诊断脚本的输出）
2. 记录错误信息和复现步骤
3. 查看[故障排除指南](./README.md)
4. 联系开发团队获取支持

---

**版本**: v2.0  
**最后更新**: 2025-07-08
