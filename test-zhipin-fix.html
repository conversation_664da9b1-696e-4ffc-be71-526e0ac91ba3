<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智联招聘域名修复测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #007acc;
            border-left: 4px solid #007acc;
            padding-left: 10px;
            margin-bottom: 15px;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.warning:hover {
            background: #e0a800;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
        .alert {
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .domain-status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .domain-allowed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .domain-external {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 智联招聘域名修复测试工具</h1>
            <p>测试在不同域名下的 cached_auth_token 持久化修复效果</p>
        </div>

        <div class="alert alert-success">
            <strong>✅ 修复说明:</strong> 
            <ul>
                <li><strong>允许域名</strong> (service.smartdeerhr.com, localhost:8006): 完整功能，包括token刷新和一致性检查</li>
                <li><strong>外部域名</strong> (zhipin.com等): 只读模式，保护现有缓存不被修改</li>
            </ul>
        </div>

        <div class="section">
            <h3>📍 当前环境状态</h3>
            <div id="envInfo"></div>
        </div>

        <div class="section">
            <h3>🔐 Token获取测试</h3>
            <button class="button" onclick="testTokenRetrieval()">测试Token获取</button>
            <button class="button" onclick="testMultipleRetrievals()">连续获取测试 (5次)</button>
            <div id="tokenResult" class="result"></div>
        </div>

        <div class="section">
            <h3>💾 缓存持久化测试</h3>
            <button class="button" onclick="testCachePersistence()">测试缓存持久化</button>
            <button class="button warning" onclick="simulatePageReload()">模拟页面重载</button>
            <div id="persistenceResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🔄 域名行为对比</h3>
            <button class="button" onclick="compareDomainBehavior()">对比不同域名行为</button>
            <div id="comparisonResult" class="result"></div>
        </div>

        <div class="section">
            <h3>📊 实时监控</h3>
            <button class="button" onclick="startCacheMonitoring()">开始缓存监控</button>
            <button class="button" onclick="stopCacheMonitoring()">停止监控</button>
            <div class="alert alert-info">
                <strong>💡 说明:</strong> 监控会每10秒检查一次缓存状态，帮助发现缓存丢失问题。
            </div>
            <div id="monitorResult" class="result"></div>
        </div>
    </div>

    <script>
        let monitoringInterval = null;

        // 显示环境信息
        function showEnvironmentInfo() {
            const envInfo = document.getElementById('envInfo');
            const currentUrl = window.location.href;
            const allowedDomains = ['https://service.smartdeerhr.com/', 'http://localhost:8006/'];
            const isAllowedDomain = allowedDomains.some(domain => currentUrl.startsWith(domain));
            const isZhipin = currentUrl.includes('zhipin.com');
            const hasAuthManager = typeof authTokenManager !== 'undefined';
            
            const domainClass = isAllowedDomain ? 'domain-allowed' : 'domain-external';
            const domainText = isAllowedDomain ? '允许域名 (完整功能)' : '外部域名 (只读模式)';
            
            envInfo.innerHTML = `
                <div class="domain-status ${domainClass}">
                    <span class="status-indicator ${isAllowedDomain ? 'status-success' : 'status-warning'}"></span>
                    <strong>域名状态:</strong> ${domainText}
                </div>
                <div><span class="status-indicator status-info"></span><strong>当前URL:</strong> ${currentUrl}</div>
                <div><span class="status-indicator ${isZhipin ? 'status-warning' : 'status-info'}"></span><strong>是否为招聘网站:</strong> ${isZhipin ? '是' : '否'}</div>
                <div><span class="status-indicator ${hasAuthManager ? 'status-success' : 'status-error'}"></span><strong>AuthManager:</strong> ${hasAuthManager ? '可用' : '不可用'}</div>
                <div><span class="status-indicator status-info"></span><strong>时间:</strong> ${new Date().toLocaleString()}</div>
            `;
        }

        // 测试Token获取
        async function testTokenRetrieval() {
            const result = document.getElementById('tokenResult');
            result.innerHTML = '🔐 测试Token获取...\n\n';
            
            try {
                if (typeof authTokenManager === 'undefined') {
                    result.innerHTML += '<span class="error">❌ authTokenManager不可用</span>\n';
                    return;
                }

                const startTime = Date.now();
                const token = await authTokenManager.getAuthToken();
                const endTime = Date.now();
                
                result.innerHTML += `<span class="success">✅ Token获取成功!</span>\n`;
                result.innerHTML += `Token: ${token.substring(0, 50)}...\n`;
                result.innerHTML += `Token长度: ${token.length}\n`;
                result.innerHTML += `获取耗时: ${endTime - startTime}ms\n\n`;
                
                // 检查缓存状态
                const cacheStatus = await authTokenManager.getCacheStatus();
                result.innerHTML += '<span class="info">📊 缓存状态:</span>\n';
                result.innerHTML += `内存缓存: ${cacheStatus.hasMemoryCache ? '✅' : '❌'}\n`;
                result.innerHTML += `持久缓存: ${cacheStatus.hasStorageCache ? '✅' : '❌'}\n`;
                result.innerHTML += `是否过期: ${cacheStatus.isExpired ? '❌ 是' : '✅ 否'}\n`;
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ Token获取失败: ${error.message}</span>\n`;
                if (error.suggestion) {
                    result.innerHTML += `建议: ${error.suggestion}\n`;
                }
            }
        }

        // 连续获取测试
        async function testMultipleRetrievals() {
            const result = document.getElementById('tokenResult');
            result.innerHTML = '🔄 连续获取Token测试 (5次)...\n\n';
            
            try {
                if (typeof authTokenManager === 'undefined') {
                    result.innerHTML += '<span class="error">❌ authTokenManager不可用</span>\n';
                    return;
                }

                const tokens = [];
                const times = [];
                
                for (let i = 1; i <= 5; i++) {
                    const startTime = Date.now();
                    const token = await authTokenManager.getAuthToken();
                    const endTime = Date.now();
                    
                    tokens.push(token);
                    times.push(endTime - startTime);
                    
                    result.innerHTML += `第${i}次: ${token.substring(0, 30)}... (${endTime - startTime}ms)\n`;
                }
                
                // 检查一致性
                const allSame = tokens.every(token => token === tokens[0]);
                result.innerHTML += `\n<span class="${allSame ? 'success' : 'error'}">Token一致性: ${allSame ? '✅ 一致' : '❌ 不一致'}</span>\n`;
                result.innerHTML += `平均耗时: ${Math.round(times.reduce((a, b) => a + b, 0) / times.length)}ms\n`;
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 连续获取测试失败: ${error.message}</span>\n`;
            }
        }

        // 测试缓存持久化
        async function testCachePersistence() {
            const result = document.getElementById('persistenceResult');
            result.innerHTML = '💾 测试缓存持久化...\n\n';
            
            try {
                if (typeof authTokenManager === 'undefined') {
                    result.innerHTML += '<span class="error">❌ authTokenManager不可用</span>\n';
                    return;
                }

                // 1. 获取初始token
                result.innerHTML += '1. 获取初始Token...\n';
                const initialToken = await authTokenManager.getAuthToken();
                result.innerHTML += `初始Token: ${initialToken.substring(0, 30)}...\n`;
                
                // 2. 检查chrome.storage.local
                result.innerHTML += '2. 检查chrome.storage.local存储...\n';
                const storageData = await new Promise((resolve) => {
                    chrome.storage.local.get(['cached_auth_token', 'token_expiry'], resolve);
                });
                
                if (storageData.cached_auth_token) {
                    result.innerHTML += `✅ chrome.storage.local中存在token: ${storageData.cached_auth_token.substring(0, 30)}...\n`;
                    
                    if (storageData.token_expiry) {
                        const expiryDate = new Date(storageData.token_expiry);
                        const now = new Date();
                        const isExpired = now > expiryDate;
                        result.innerHTML += `过期时间: ${expiryDate.toLocaleString()} ${isExpired ? '(已过期)' : '(有效)'}\n`;
                    }
                } else {
                    result.innerHTML += '❌ chrome.storage.local中未找到token\n';
                }
                
                // 3. 清除内存缓存，测试持久化恢复
                result.innerHTML += '3. 清除内存缓存，测试持久化恢复...\n';
                authTokenManager.memoryCache = {}; // 直接清除内存缓存
                
                const recoveredToken = await authTokenManager.getAuthToken();
                result.innerHTML += `恢复Token: ${recoveredToken.substring(0, 30)}...\n`;
                
                if (initialToken === recoveredToken) {
                    result.innerHTML += '<span class="success">✅ 缓存持久化测试通过</span>\n';
                } else {
                    result.innerHTML += '<span class="warning">⚠️ Token不一致，可能发生了刷新</span>\n';
                }
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 缓存持久化测试失败: ${error.message}</span>\n`;
            }
        }

        // 模拟页面重载
        async function simulatePageReload() {
            const result = document.getElementById('persistenceResult');
            result.innerHTML = '🔄 模拟页面重载...\n\n';
            
            try {
                if (typeof authTokenManager === 'undefined') {
                    result.innerHTML += '<span class="error">❌ authTokenManager不可用</span>\n';
                    return;
                }

                // 清除内存缓存（模拟页面重载）
                authTokenManager.memoryCache = {};
                result.innerHTML += '✅ 内存缓存已清除 (模拟页面重载)\n';
                
                // 尝试获取token
                const token = await authTokenManager.getAuthToken();
                result.innerHTML += `<span class="success">✅ 页面重载后成功获取Token: ${token.substring(0, 30)}...</span>\n`;
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 页面重载模拟失败: ${error.message}</span>\n`;
            }
        }

        // 对比不同域名行为
        async function compareDomainBehavior() {
            const result = document.getElementById('comparisonResult');
            result.innerHTML = '🔄 对比不同域名行为...\n\n';
            
            try {
                if (typeof authTokenManager === 'undefined') {
                    result.innerHTML += '<span class="error">❌ authTokenManager不可用</span>\n';
                    return;
                }

                const currentUrl = window.location.href;
                const allowedDomains = ['https://service.smartdeerhr.com/', 'http://localhost:8006/'];
                const isAllowedDomain = allowedDomains.some(domain => currentUrl.startsWith(domain));
                
                result.innerHTML += `<span class="info">📍 当前域名状态:</span>\n`;
                result.innerHTML += `域名: ${currentUrl}\n`;
                result.innerHTML += `类型: ${isAllowedDomain ? '允许域名' : '外部域名'}\n`;
                result.innerHTML += `预期行为: ${isAllowedDomain ? '完整功能 (可刷新token)' : '只读模式 (保护缓存)'}\n\n`;
                
                // 测试token获取
                result.innerHTML += `<span class="info">🔐 测试Token获取:</span>\n`;
                const startTime = Date.now();
                const token = await authTokenManager.getAuthToken();
                const endTime = Date.now();
                
                result.innerHTML += `Token: ${token.substring(0, 40)}...\n`;
                result.innerHTML += `获取耗时: ${endTime - startTime}ms\n\n`;
                
                // 检查缓存状态
                const cacheStatus = await authTokenManager.getCacheStatus();
                result.innerHTML += `<span class="info">📊 缓存状态:</span>\n`;
                result.innerHTML += `内存缓存: ${cacheStatus.hasMemoryCache ? '✅' : '❌'}\n`;
                result.innerHTML += `持久缓存: ${cacheStatus.hasStorageCache ? '✅' : '❌'}\n`;
                result.innerHTML += `是否过期: ${cacheStatus.isExpired ? '❌ 是' : '✅ 否'}\n`;
                result.innerHTML += `需要刷新: ${cacheStatus.needsRefresh ? '⚠️ 是' : '✅ 否'}\n\n`;
                
                // 行为验证
                if (isAllowedDomain) {
                    result.innerHTML += `<span class="success">✅ 允许域名行为正常</span>\n`;
                    result.innerHTML += `- 支持token一致性检查\n`;
                    result.innerHTML += `- 支持后台token刷新\n`;
                    result.innerHTML += `- 支持缓存更新操作\n`;
                } else {
                    result.innerHTML += `<span class="warning">🔒 外部域名只读模式</span>\n`;
                    result.innerHTML += `- 跳过token一致性检查\n`;
                    result.innerHTML += `- 跳过后台token刷新\n`;
                    result.innerHTML += `- 保护现有缓存不被修改\n`;
                }
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 域名行为对比失败: ${error.message}</span>\n`;
            }
        }

        // 开始缓存监控
        function startCacheMonitoring() {
            if (monitoringInterval) {
                stopCacheMonitoring();
            }
            
            const result = document.getElementById('monitorResult');
            result.innerHTML = '📊 开始缓存监控...\n\n';
            
            let checkCount = 0;
            monitoringInterval = setInterval(async () => {
                checkCount++;
                const timestamp = new Date().toLocaleTimeString();
                
                try {
                    if (typeof authTokenManager === 'undefined') {
                        result.innerHTML += `[${timestamp}] ❌ authTokenManager不可用\n`;
                        return;
                    }

                    const cacheStatus = await authTokenManager.getCacheStatus();
                    const memoryStatus = cacheStatus.hasMemoryCache ? '✅' : '❌';
                    const storageStatus = cacheStatus.hasStorageCache ? '✅' : '❌';
                    
                    result.innerHTML += `[${timestamp}] #${checkCount}: 内存${memoryStatus} 持久${storageStatus}`;
                    
                    if (cacheStatus.isExpired) {
                        result.innerHTML += ' (已过期)';
                    }
                    
                    result.innerHTML += '\n';
                    
                    // 如果检测到持久缓存丢失，发出警告
                    if (!cacheStatus.hasStorageCache && checkCount > 1) {
                        result.innerHTML += `<span class="error">[${timestamp}] ⚠️ 警告: 检测到持久缓存丢失!</span>\n`;
                    }
                    
                    // 保持结果区域滚动到底部
                    result.scrollTop = result.scrollHeight;
                    
                } catch (error) {
                    result.innerHTML += `[${timestamp}] ❌ 监控检查失败: ${error.message}\n`;
                }
            }, 10000); // 每10秒检查一次
            
            result.innerHTML += '<span class="info">✅ 监控已启动，每10秒检查一次</span>\n';
        }

        // 停止缓存监控
        function stopCacheMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                
                const result = document.getElementById('monitorResult');
                result.innerHTML += '<span class="warning">⏹️ 监控已停止</span>\n';
            }
        }

        // 页面加载时显示环境信息
        document.addEventListener('DOMContentLoaded', function() {
            showEnvironmentInfo();
        });

        // 页面卸载时停止监控
        window.addEventListener('beforeunload', function() {
            stopCacheMonitoring();
        });
    </script>
</body>
</html>
