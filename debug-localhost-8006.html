<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>localhost:8006 Token调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #007acc;
            border-left: 4px solid #007acc;
            padding-left: 10px;
            margin-bottom: 15px;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 localhost:8006 Token调试工具</h1>
            <p>专门用于调试localhost:8006域名下的身份验证问题</p>
        </div>

        <div class="section">
            <h3>📍 环境信息</h3>
            <div id="envInfo"></div>
        </div>

        <div class="section">
            <h3>🔍 localStorage检查</h3>
            <button class="button" onclick="checkLocalStorage()">检查localStorage中的Token</button>
            <button class="button" onclick="decryptAllKeys()">解密所有localStorage键值</button>
            <div id="localStorageResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🔐 插件认证测试</h3>
            <button class="button" onclick="testAuthToken()">测试获取AuthToken</button>
            <button class="button" onclick="checkCacheStatus()">检查缓存状态</button>
            <button class="button" onclick="forceConsistencyCheck()">强制一致性检查</button>
            <div id="authTestResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🧹 缓存管理</h3>
            <button class="button danger" onclick="clearAllCache()">清除所有缓存</button>
            <button class="button" onclick="refreshToken()">刷新Token</button>
            <div id="cacheResult" class="result"></div>
        </div>

        <div class="section">
            <h3>🚀 API测试</h3>
            <button class="button success" onclick="testApiCall()">测试API调用</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="section">
            <h3>📊 综合诊断</h3>
            <button class="button info" onclick="runFullDiagnostic()">运行完整诊断</button>
            <div id="diagnosticResult" class="result"></div>
        </div>
    </div>

    <script>
        // 显示环境信息
        function showEnvironmentInfo() {
            const envInfo = document.getElementById('envInfo');
            envInfo.innerHTML = `
                <div><span class="status-indicator status-info"></span><strong>当前域名:</strong> ${window.location.href}</div>
                <div><span class="status-indicator status-info"></span><strong>用户代理:</strong> ${navigator.userAgent}</div>
                <div><span class="status-indicator status-info"></span><strong>时间:</strong> ${new Date().toLocaleString()}</div>
            `;
        }

        // 检查localStorage中的token
        function checkLocalStorage() {
            const result = document.getElementById('localStorageResult');
            result.innerHTML = '🔍 检查localStorage中的Token...\n\n';
            
            let foundTokens = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (!key) continue;
                
                try {
                    const value = localStorage.getItem(key);
                    result.innerHTML += `Key: ${key}\nValue: ${value ? value.substring(0, 100) + '...' : 'null'}\n\n`;
                    
                    // 尝试解密key
                    try {
                        const decryptedKey = Base64EncoderDecoder.decrypt(key);
                        result.innerHTML += `解密后的Key: ${decryptedKey}\n`;
                        
                        if (decryptedKey === 'auth_token') {
                            result.innerHTML += `<span class="success">✅ 找到auth_token!</span>\n`;
                            
                            // 尝试解密value
                            try {
                                const decryptedValue = Base64EncoderDecoder.decrypt(value);
                                result.innerHTML += `解密后的Value: ${decryptedValue}\n`;
                                foundTokens.push({
                                    key: key,
                                    decryptedKey: decryptedKey,
                                    decryptedValue: decryptedValue
                                });
                            } catch (valueDecryptError) {
                                result.innerHTML += `<span class="error">❌ 解密Value失败: ${valueDecryptError.message}</span>\n`;
                            }
                        }
                    } catch (keyDecryptError) {
                        // 解密失败，跳过
                    }
                    
                    result.innerHTML += '---\n';
                } catch (error) {
                    result.innerHTML += `<span class="error">❌ 处理key失败: ${error.message}</span>\n`;
                }
            }
            
            result.innerHTML += `\n<span class="info">📊 总结: 找到 ${foundTokens.length} 个auth_token</span>\n`;
            
            if (foundTokens.length > 0) {
                result.innerHTML += `<span class="success">✅ localStorage中存在有效的认证令牌</span>\n`;
            } else {
                result.innerHTML += `<span class="error">❌ localStorage中未找到有效的认证令牌</span>\n`;
            }
        }

        // 解密所有localStorage键值
        function decryptAllKeys() {
            const result = document.getElementById('localStorageResult');
            result.innerHTML = '🔓 解密所有localStorage键值...\n\n';
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (!key) continue;
                
                const value = localStorage.getItem(key);
                result.innerHTML += `原始Key: ${key}\n`;
                
                try {
                    const decryptedKey = Base64EncoderDecoder.decrypt(key);
                    result.innerHTML += `解密Key: ${decryptedKey}\n`;
                    
                    if (value) {
                        try {
                            const decryptedValue = Base64EncoderDecoder.decrypt(value);
                            result.innerHTML += `解密Value: ${decryptedValue.substring(0, 200)}${decryptedValue.length > 200 ? '...' : ''}\n`;
                        } catch (valueError) {
                            result.innerHTML += `Value解密失败: ${valueError.message}\n`;
                        }
                    }
                } catch (keyError) {
                    result.innerHTML += `Key解密失败: ${keyError.message}\n`;
                }
                
                result.innerHTML += '---\n';
            }
        }

        // 测试获取AuthToken
        async function testAuthToken() {
            const result = document.getElementById('authTestResult');
            result.innerHTML = '🔐 测试获取AuthToken...\n\n';
            
            try {
                const token = await authTokenManager.getAuthToken();
                result.innerHTML += `<span class="success">✅ 成功获取AuthToken!</span>\n`;
                result.innerHTML += `Token: ${token.substring(0, 50)}...\n`;
                result.innerHTML += `Token长度: ${token.length}\n`;
                result.innerHTML += `Token类型: ${typeof token}\n`;
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 获取AuthToken失败: ${error.message}</span>\n`;
                result.innerHTML += `错误类型: ${error.constructor.name}\n`;
                if (error.suggestion) {
                    result.innerHTML += `建议: ${error.suggestion}\n`;
                }
            }
        }

        // 检查缓存状态
        async function checkCacheStatus() {
            const result = document.getElementById('authTestResult');
            result.innerHTML = '📊 检查缓存状态...\n\n';
            
            try {
                const status = await authTokenManager.getCacheStatus();
                result.innerHTML += `<span class="info">📊 缓存状态:</span>\n`;
                result.innerHTML += JSON.stringify(status, null, 2) + '\n';
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 检查缓存状态失败: ${error.message}</span>\n`;
            }
        }

        // 强制一致性检查
        async function forceConsistencyCheck() {
            const result = document.getElementById('authTestResult');
            result.innerHTML = '🔄 强制一致性检查...\n\n';
            
            try {
                const checkResult = await authTokenManager.forceTokenConsistencyCheck();
                result.innerHTML += `<span class="info">🔄 一致性检查结果:</span>\n`;
                result.innerHTML += JSON.stringify(checkResult, null, 2) + '\n';
                
                if (checkResult.wasInconsistent) {
                    result.innerHTML += `<span class="warning">⚠️ 检测到不一致，已自动同步</span>\n`;
                } else {
                    result.innerHTML += `<span class="success">✅ Token一致，无需同步</span>\n`;
                }
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 一致性检查失败: ${error.message}</span>\n`;
            }
        }

        // 清除所有缓存
        async function clearAllCache() {
            const result = document.getElementById('cacheResult');
            result.innerHTML = '🧹 清除所有缓存...\n\n';
            
            try {
                await authTokenManager.clearCache();
                result.innerHTML += `<span class="success">✅ 所有缓存已清除</span>\n`;
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 清除缓存失败: ${error.message}</span>\n`;
            }
        }

        // 刷新Token
        async function refreshToken() {
            const result = document.getElementById('cacheResult');
            result.innerHTML = '🔄 刷新Token...\n\n';
            
            try {
                // 先清除缓存
                await authTokenManager.clearCache();
                result.innerHTML += '1. 缓存已清除\n';
                
                // 重新获取token
                const newToken = await authTokenManager.getAuthToken();
                result.innerHTML += `<span class="success">✅ Token刷新成功!</span>\n`;
                result.innerHTML += `新Token: ${newToken.substring(0, 50)}...\n`;
            } catch (error) {
                result.innerHTML += `<span class="error">❌ Token刷新失败: ${error.message}</span>\n`;
            }
        }

        // 测试API调用
        async function testApiCall() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '🚀 测试API调用...\n\n';
            
            try {
                // 这里需要根据实际的API调用方法进行调整
                result.innerHTML += '准备测试API调用...\n';
                
                // 如果有request对象，可以测试
                if (typeof request !== 'undefined' && request.postV2) {
                    const response = await request.postV2({
                        data: {
                            functionKey: 'test_endpoint',
                            params: {}
                        }
                    });
                    result.innerHTML += `<span class="success">✅ API调用成功!</span>\n`;
                    result.innerHTML += `响应: ${JSON.stringify(response, null, 2)}\n`;
                } else {
                    result.innerHTML += `<span class="warning">⚠️ request对象不可用，无法测试API调用</span>\n`;
                    result.innerHTML += '请确保插件已正确加载\n';
                }
            } catch (error) {
                result.innerHTML += `<span class="error">❌ API调用失败: ${error.message}</span>\n`;
            }
        }

        // 运行完整诊断
        async function runFullDiagnostic() {
            const result = document.getElementById('diagnosticResult');
            result.innerHTML = '🔍 运行完整诊断...\n\n';
            
            try {
                // 1. 环境检查
                result.innerHTML += '1️⃣ 环境检查\n';
                result.innerHTML += `当前域名: ${window.location.href}\n`;
                result.innerHTML += `是否为目标域名: ${window.location.href.includes('localhost:8006') ? '✅ 是' : '❌ 否'}\n\n`;
                
                // 2. localStorage检查
                result.innerHTML += '2️⃣ localStorage检查\n';
                let hasAuthToken = false;
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (!key) continue;
                    
                    try {
                        const decryptedKey = Base64EncoderDecoder.decrypt(key);
                        if (decryptedKey === 'auth_token') {
                            hasAuthToken = true;
                            break;
                        }
                    } catch (error) {
                        continue;
                    }
                }
                result.innerHTML += `localStorage中有auth_token: ${hasAuthToken ? '✅ 是' : '❌ 否'}\n\n`;
                
                // 3. 插件状态检查
                result.innerHTML += '3️⃣ 插件状态检查\n';
                const hasAuthManager = typeof authTokenManager !== 'undefined';
                result.innerHTML += `authTokenManager可用: ${hasAuthManager ? '✅ 是' : '❌ 否'}\n`;
                
                if (hasAuthManager) {
                    try {
                        const status = await authTokenManager.getCacheStatus();
                        result.innerHTML += `缓存状态: ${JSON.stringify(status, null, 2)}\n`;
                    } catch (error) {
                        result.innerHTML += `缓存状态检查失败: ${error.message}\n`;
                    }
                }
                
                result.innerHTML += '\n';
                
                // 4. Token获取测试
                result.innerHTML += '4️⃣ Token获取测试\n';
                if (hasAuthManager) {
                    try {
                        const token = await authTokenManager.getAuthToken();
                        result.innerHTML += `<span class="success">✅ Token获取成功: ${token.substring(0, 30)}...</span>\n`;
                    } catch (error) {
                        result.innerHTML += `<span class="error">❌ Token获取失败: ${error.message}</span>\n`;
                    }
                } else {
                    result.innerHTML += `<span class="error">❌ authTokenManager不可用</span>\n`;
                }
                
                result.innerHTML += '\n<span class="info">📊 诊断完成</span>\n';
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 诊断过程中发生错误: ${error.message}</span>\n`;
            }
        }

        // 页面加载时显示环境信息
        document.addEventListener('DOMContentLoaded', function() {
            showEnvironmentInfo();
        });
    </script>
</body>
</html>
